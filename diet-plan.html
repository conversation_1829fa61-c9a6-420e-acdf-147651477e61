<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>饮食计划</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .gradient-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .tab-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        .meal-card {
            transition: all 0.3s ease;
        }
        .meal-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 pb-20">
    <!-- 顶部导航 -->
    <div class="gradient-header text-white">
        <div class="flex items-center justify-between px-4 py-3">
            <h1 class="text-xl font-bold">饮食计划</h1>
            <div class="flex items-center space-x-3">
                <i class="fas fa-calendar-alt text-lg"></i>
                <i class="fas fa-cog text-lg"></i>
            </div>
        </div>
        
        <!-- 日期选择器 -->
        <div class="px-4 pb-4">
            <div class="flex items-center justify-between mb-3">
                <button class="text-purple-200">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <h2 class="text-lg font-semibold">2024年1月15日</h2>
                <button class="text-purple-200">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            
            <!-- 周视图 -->
            <div class="flex justify-between">
                <div class="text-center">
                    <p class="text-xs text-purple-200 mb-1">周一</p>
                    <div class="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                        <span class="text-sm">13</span>
                    </div>
                </div>
                <div class="text-center">
                    <p class="text-xs text-purple-200 mb-1">周二</p>
                    <div class="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                        <span class="text-sm">14</span>
                    </div>
                </div>
                <div class="text-center">
                    <p class="text-xs text-purple-200 mb-1">周三</p>
                    <div class="w-8 h-8 rounded-full bg-white flex items-center justify-center">
                        <span class="text-sm text-purple-600 font-bold">15</span>
                    </div>
                </div>
                <div class="text-center">
                    <p class="text-xs text-purple-200 mb-1">周四</p>
                    <div class="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                        <span class="text-sm">16</span>
                    </div>
                </div>
                <div class="text-center">
                    <p class="text-xs text-purple-200 mb-1">周五</p>
                    <div class="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                        <span class="text-sm">17</span>
                    </div>
                </div>
                <div class="text-center">
                    <p class="text-xs text-purple-200 mb-1">周六</p>
                    <div class="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                        <span class="text-sm">18</span>
                    </div>
                </div>
                <div class="text-center">
                    <p class="text-xs text-purple-200 mb-1">周日</p>
                    <div class="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                        <span class="text-sm">19</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="px-4 py-6 space-y-6">
        <!-- 今日营养目标 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-target text-green-500 mr-2"></i>
                今日营养目标
            </h2>
            
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="relative w-16 h-16 mx-auto mb-2">
                        <svg class="w-16 h-16 transform -rotate-90">
                            <circle cx="32" cy="32" r="28" stroke="#e5e7eb" stroke-width="4" fill="none"/>
                            <circle cx="32" cy="32" r="28" stroke="#3b82f6" stroke-width="4" fill="none"
                                    stroke-dasharray="175.9" stroke-dashoffset="109.5"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xs font-bold text-blue-600">38%</span>
                        </div>
                    </div>
                    <p class="text-xs text-gray-600">热量</p>
                    <p class="text-sm font-semibold">680/1800</p>
                </div>
                
                <div class="text-center">
                    <div class="relative w-16 h-16 mx-auto mb-2">
                        <svg class="w-16 h-16 transform -rotate-90">
                            <circle cx="32" cy="32" r="28" stroke="#e5e7eb" stroke-width="4" fill="none"/>
                            <circle cx="32" cy="32" r="28" stroke="#10b981" stroke-width="4" fill="none"
                                    stroke-dasharray="175.9" stroke-dashoffset="109.5"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xs font-bold text-green-600">38%</span>
                        </div>
                    </div>
                    <p class="text-xs text-gray-600">蛋白质</p>
                    <p class="text-sm font-semibold">28/75g</p>
                </div>
                
                <div class="text-center">
                    <div class="relative w-16 h-16 mx-auto mb-2">
                        <svg class="w-16 h-16 transform -rotate-90">
                            <circle cx="32" cy="32" r="28" stroke="#e5e7eb" stroke-width="4" fill="none"/>
                            <circle cx="32" cy="32" r="28" stroke="#f59e0b" stroke-width="4" fill="none"
                                    stroke-dasharray="175.9" stroke-dashoffset="140.7"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xs font-bold text-yellow-600">20%</span>
                        </div>
                    </div>
                    <p class="text-xs text-gray-600">脂肪</p>
                    <p class="text-sm font-semibold">12/60g</p>
                </div>
            </div>
        </div>

        <!-- 早餐 -->
        <div class="bg-white rounded-2xl shadow-sm meal-card">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-sun text-yellow-500 mr-2"></i>
                        早餐
                    </h3>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-green-600 bg-green-100 px-2 py-1 rounded-full">已完成</span>
                        <i class="fas fa-check-circle text-green-500"></i>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1551024506-0bccd828d307?w=50&h=50&fit=crop&crop=center"
                                 class="w-12 h-12 rounded-lg object-cover mr-3">
                            <div>
                                <p class="font-medium text-gray-800">燕麦粥</p>
                                <p class="text-sm text-gray-600">100g</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-800">389 kcal</p>
                            <p class="text-xs text-gray-500">碳水 66g</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1569288052389-dac9b01ac4a9?w=50&h=50&fit=crop&crop=center"
                                 class="w-12 h-12 rounded-lg object-cover mr-3">
                            <div>
                                <p class="font-medium text-gray-800">蓝莓</p>
                                <p class="text-sm text-gray-600">50g</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-800">29 kcal</p>
                            <p class="text-xs text-gray-500">碳水 7g</p>
                        </div>
                    </div>
                </div>

                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">总热量</span>
                        <span class="font-semibold text-gray-800">418 kcal</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 午餐 -->
        <div class="bg-white rounded-2xl shadow-sm meal-card">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-sun text-orange-500 mr-2"></i>
                        午餐
                    </h3>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full">待记录</span>
                        <button class="text-purple-600">
                            <i class="fas fa-plus-circle text-lg"></i>
                        </button>
                    </div>
                </div>

                <!-- 推荐菜单 -->
                <div class="bg-purple-50 rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-lightbulb text-purple-600 mr-2"></i>
                        <span class="text-sm font-medium text-purple-800">AI推荐菜单</span>
                    </div>

                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">糙米饭 (100g)</span>
                            <span class="text-sm text-gray-600">111 kcal</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">清蒸鲈鱼 (150g)</span>
                            <span class="text-sm text-gray-600">125 kcal</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">菠菜豆腐汤 (200ml)</span>
                            <span class="text-sm text-gray-600">45 kcal</span>
                        </div>
                    </div>

                    <button class="w-full mt-3 bg-purple-600 text-white py-2 rounded-lg text-sm font-medium">
                        采用推荐菜单
                    </button>
                </div>
            </div>
        </div>

        <!-- 晚餐 -->
        <div class="bg-white rounded-2xl shadow-sm meal-card">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-moon text-blue-500 mr-2"></i>
                        晚餐
                    </h3>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">未开始</span>
                        <button class="text-gray-400">
                            <i class="fas fa-plus-circle text-lg"></i>
                        </button>
                    </div>
                </div>

                <div class="text-center py-8">
                    <i class="fas fa-utensils text-gray-300 text-3xl mb-3"></i>
                    <p class="text-gray-500">晚餐时间还未到</p>
                    <p class="text-sm text-gray-400">建议在18:00-19:00之间用餐</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar fixed bottom-0 left-0 right-0 px-4 py-2">
        <div class="flex items-center justify-around">
            <div class="text-center">
                <i class="fas fa-home text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">首页</p>
            </div>
            <div class="text-center">
                <i class="fas fa-utensils text-purple-600 text-xl mb-1"></i>
                <p class="text-xs text-purple-600 font-medium">饮食</p>
            </div>
            <div class="text-center">
                <i class="fas fa-search text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">查询</p>
            </div>
            <div class="text-center">
                <i class="fas fa-chart-bar text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">健康</p>
            </div>
            <div class="text-center">
                <i class="fas fa-user text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">我的</p>
            </div>
        </div>
    </div>
</body>
</html>
