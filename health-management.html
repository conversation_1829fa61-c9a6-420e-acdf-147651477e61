<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .gradient-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .tab-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        .health-card {
            transition: all 0.3s ease;
        }
        .health-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .chart-container {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
    </style>
</head>
<body class="bg-gray-50 pb-20">
    <!-- 顶部导航 -->
    <div class="gradient-header text-white">
        <div class="flex items-center justify-between px-4 py-3">
            <h1 class="text-xl font-bold">健康管理</h1>
            <div class="flex items-center space-x-3">
                <i class="fas fa-camera text-lg"></i>
                <i class="fas fa-plus text-lg"></i>
            </div>
        </div>
        
        <!-- 健康评分概览 -->
        <div class="px-4 pb-6">
            <div class="bg-white bg-opacity-20 rounded-2xl p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold mb-1">健康评分</h2>
                        <div class="flex items-baseline">
                            <span class="text-3xl font-bold">85</span>
                            <span class="text-sm ml-1">分</span>
                            <span class="text-green-200 text-sm ml-2">↑ 5分</span>
                        </div>
                        <p class="text-purple-100 text-sm">较上周有所改善</p>
                    </div>
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-heartbeat text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="px-4 py-6 space-y-6">
        <!-- 用药管理 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm health-card">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-pills text-purple-600 mr-2"></i>
                    用药管理
                </h2>
                <button class="text-purple-600 text-sm">管理</button>
            </div>
            
            <!-- 今日用药提醒 -->
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-exclamation text-red-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">二甲双胍</p>
                            <p class="text-sm text-gray-600">早餐后 500mg</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-red-600 font-medium">未服用</p>
                        <button class="text-xs text-red-600 underline">标记已服用</button>
                    </div>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-check text-green-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">阿司匹林</p>
                            <p class="text-sm text-gray-600">晚餐后 100mg</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-green-600 font-medium">已服用</p>
                        <p class="text-xs text-gray-500">19:30</p>
                    </div>
                </div>
            </div>
            
            <button class="w-full mt-4 bg-purple-100 text-purple-700 py-2 rounded-lg text-sm font-medium">
                查看用药历史
            </button>
        </div>

        <!-- 健康指标录入 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm health-card">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                    健康指标
                </h2>
                <button class="text-blue-600 text-sm">录入</button>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-50 rounded-xl p-4 text-center">
                    <i class="fas fa-tint text-red-500 text-2xl mb-2"></i>
                    <p class="text-sm text-gray-600 mb-1">血糖</p>
                    <p class="text-xl font-bold text-gray-800">6.8</p>
                    <p class="text-xs text-gray-500">mmol/L</p>
                    <p class="text-xs text-red-600 mt-1">偏高</p>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-4 text-center">
                    <i class="fas fa-heartbeat text-red-500 text-2xl mb-2"></i>
                    <p class="text-sm text-gray-600 mb-1">血压</p>
                    <p class="text-xl font-bold text-gray-800">128/82</p>
                    <p class="text-xs text-gray-500">mmHg</p>
                    <p class="text-xs text-yellow-600 mt-1">正常</p>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-4 text-center">
                    <i class="fas fa-weight text-blue-500 text-2xl mb-2"></i>
                    <p class="text-sm text-gray-600 mb-1">体重</p>
                    <p class="text-xl font-bold text-gray-800">68.2</p>
                    <p class="text-xs text-gray-500">kg</p>
                    <p class="text-xs text-green-600 mt-1">↓ 0.3kg</p>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-4 text-center">
                    <i class="fas fa-thermometer-half text-green-500 text-2xl mb-2"></i>
                    <p class="text-sm text-gray-600 mb-1">BMI</p>
                    <p class="text-xl font-bold text-gray-800">23.6</p>
                    <p class="text-xs text-gray-500">kg/m²</p>
                    <p class="text-xs text-green-600 mt-1">正常</p>
                </div>
            </div>
        </div>

        <!-- 健康趋势图表 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm health-card">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-chart-area text-green-600 mr-2"></i>
                    健康趋势
                </h2>
                <select class="text-sm text-gray-600 border border-gray-300 rounded-lg px-2 py-1">
                    <option>最近7天</option>
                    <option>最近30天</option>
                    <option>最近3个月</option>
                </select>
            </div>
            
            <!-- 模拟图表 -->
            <div class="chart-container rounded-xl p-4 text-white mb-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm">血糖趋势</span>
                    <span class="text-sm">6.8 mmol/L</span>
                </div>
                <div class="h-24 flex items-end justify-between">
                    <div class="w-6 bg-white bg-opacity-30 rounded-t" style="height: 60%"></div>
                    <div class="w-6 bg-white bg-opacity-30 rounded-t" style="height: 70%"></div>
                    <div class="w-6 bg-white bg-opacity-30 rounded-t" style="height: 50%"></div>
                    <div class="w-6 bg-white bg-opacity-30 rounded-t" style="height: 80%"></div>
                    <div class="w-6 bg-white bg-opacity-30 rounded-t" style="height: 65%"></div>
                    <div class="w-6 bg-white bg-opacity-30 rounded-t" style="height: 75%"></div>
                    <div class="w-6 bg-white bg-opacity-50 rounded-t" style="height: 85%"></div>
                </div>
            </div>
            
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <p class="text-sm text-gray-600">平均值</p>
                    <p class="font-semibold text-gray-800">6.5</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">最高值</p>
                    <p class="font-semibold text-gray-800">7.2</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600">最低值</p>
                    <p class="font-semibold text-gray-800">5.8</p>
                </div>
            </div>
        </div>

        <!-- 体检报告 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm health-card">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-file-medical text-orange-600 mr-2"></i>
                    体检报告
                </h2>
                <button class="text-orange-600 text-sm">上传</button>
            </div>
            
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-file-pdf text-red-500 text-xl mr-3"></i>
                        <div>
                            <p class="font-medium text-gray-800">年度体检报告</p>
                            <p class="text-sm text-gray-600">2024-01-10</p>
                        </div>
                    </div>
                    <button class="text-purple-600 text-sm">查看</button>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-file-image text-blue-500 text-xl mr-3"></i>
                        <div>
                            <p class="font-medium text-gray-800">血常规检查</p>
                            <p class="text-sm text-gray-600">2023-12-15</p>
                        </div>
                    </div>
                    <button class="text-purple-600 text-sm">查看</button>
                </div>
            </div>
            
            <button class="w-full mt-4 bg-orange-100 text-orange-700 py-2 rounded-lg text-sm font-medium flex items-center justify-center">
                <i class="fas fa-camera mr-2"></i>
                拍照智能录入
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar fixed bottom-0 left-0 right-0 px-4 py-2">
        <div class="flex items-center justify-around">
            <div class="text-center">
                <i class="fas fa-home text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">首页</p>
            </div>
            <div class="text-center">
                <i class="fas fa-utensils text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">饮食</p>
            </div>
            <div class="text-center">
                <i class="fas fa-search text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">查询</p>
            </div>
            <div class="text-center">
                <i class="fas fa-chart-bar text-purple-600 text-xl mb-1"></i>
                <p class="text-xs text-purple-600 font-medium">健康</p>
            </div>
            <div class="text-center">
                <i class="fas fa-user text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">我的</p>
            </div>
        </div>
    </div>
</body>
</html>
