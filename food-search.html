<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>食物查询</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .search-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
        .tab-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        .food-card {
            transition: all 0.3s ease;
        }
        .food-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .safe-food { border-left: 4px solid #10b981; }
        .caution-food { border-left: 4px solid #f59e0b; }
        .avoid-food { border-left: 4px solid #ef4444; }
    </style>
</head>
<body class="bg-gray-50 pb-20">
    <!-- 搜索栏 -->
    <div class="search-bar sticky top-0 z-10 px-4 py-4 border-b border-gray-200">
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
            <input type="text" 
                   class="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                   placeholder="搜索食物、菜品或营养成分">
        </div>
        
        <!-- 快捷筛选 -->
        <div class="flex space-x-2 mt-3 overflow-x-auto">
            <button class="flex-shrink-0 px-4 py-2 bg-purple-600 text-white rounded-full text-sm font-medium">
                全部
            </button>
            <button class="flex-shrink-0 px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm">
                推荐食物
            </button>
            <button class="flex-shrink-0 px-4 py-2 bg-yellow-100 text-yellow-700 rounded-full text-sm">
                谨慎食用
            </button>
            <button class="flex-shrink-0 px-4 py-2 bg-red-100 text-red-700 rounded-full text-sm">
                避免食用
            </button>
            <button class="flex-shrink-0 px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm">
                低糖食物
            </button>
        </div>
    </div>

    <div class="px-4 py-6 space-y-4">
        <!-- 热门搜索 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-fire text-red-500 mr-2"></i>
                热门搜索
            </h2>
            
            <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">苹果</span>
                <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">鸡胸肉</span>
                <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">燕麦</span>
                <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">西兰花</span>
                <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">三文鱼</span>
                <span class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">糙米</span>
            </div>
        </div>

        <!-- 推荐食物列表 -->
        <div class="space-y-3">
            <h2 class="text-lg font-semibold text-gray-800 px-2">为您推荐</h2>
            
            <!-- 推荐食物 -->
            <div class="bg-white rounded-xl shadow-sm food-card safe-food">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=60&h=60&fit=crop&crop=center" 
                                 class="w-16 h-16 rounded-lg object-cover mr-4">
                            <div>
                                <h3 class="font-semibold text-gray-800">苹果</h3>
                                <p class="text-sm text-gray-600">每100g</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">推荐</span>
                                    <span class="text-xs text-gray-500 ml-2">低糖水果</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-gray-800">52 kcal</p>
                            <p class="text-xs text-gray-500">碳水 14g</p>
                            <p class="text-xs text-gray-500">糖分 10g</p>
                        </div>
                    </div>
                    
                    <div class="mt-3 pt-3 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-green-700">✓ 适合糖尿病患者</span>
                            <button class="text-purple-600 text-sm font-medium">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 谨慎食用 -->
            <div class="bg-white rounded-xl shadow-sm food-card caution-food">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1586190848861-99aa4a171e90?w=60&h=60&fit=crop&crop=center" 
                                 class="w-16 h-16 rounded-lg object-cover mr-4">
                            <div>
                                <h3 class="font-semibold text-gray-800">香蕉</h3>
                                <p class="text-sm text-gray-600">每100g</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">谨慎</span>
                                    <span class="text-xs text-gray-500 ml-2">中等糖分</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-gray-800">89 kcal</p>
                            <p class="text-xs text-gray-500">碳水 23g</p>
                            <p class="text-xs text-gray-500">糖分 12g</p>
                        </div>
                    </div>
                    
                    <div class="mt-3 pt-3 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-yellow-700">⚠ 建议少量食用</span>
                            <button class="text-purple-600 text-sm font-medium">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 避免食用 -->
            <div class="bg-white rounded-xl shadow-sm food-card avoid-food">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=60&h=60&fit=crop&crop=center" 
                                 class="w-16 h-16 rounded-lg object-cover mr-4">
                            <div>
                                <h3 class="font-semibold text-gray-800">蛋糕</h3>
                                <p class="text-sm text-gray-600">每100g</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">避免</span>
                                    <span class="text-xs text-gray-500 ml-2">高糖高脂</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-gray-800">347 kcal</p>
                            <p class="text-xs text-gray-500">碳水 46g</p>
                            <p class="text-xs text-gray-500">糖分 35g</p>
                        </div>
                    </div>
                    
                    <div class="mt-3 pt-3 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-red-700">✗ 不建议食用</span>
                            <button class="text-purple-600 text-sm font-medium">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推荐食物 -->
            <div class="bg-white rounded-xl shadow-sm food-card safe-food">
                <div class="p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1628773822503-930a7eaecf80?w=60&h=60&fit=crop&crop=center" 
                                 class="w-16 h-16 rounded-lg object-cover mr-4">
                            <div>
                                <h3 class="font-semibold text-gray-800">西兰花</h3>
                                <p class="text-sm text-gray-600">每100g</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">推荐</span>
                                    <span class="text-xs text-gray-500 ml-2">低卡蔬菜</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-gray-800">34 kcal</p>
                            <p class="text-xs text-gray-500">碳水 7g</p>
                            <p class="text-xs text-gray-500">纤维 3g</p>
                        </div>
                    </div>
                    
                    <div class="mt-3 pt-3 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-green-700">✓ 富含维生素C</span>
                            <button class="text-purple-600 text-sm font-medium">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar fixed bottom-0 left-0 right-0 px-4 py-2">
        <div class="flex items-center justify-around">
            <div class="text-center">
                <i class="fas fa-home text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">首页</p>
            </div>
            <div class="text-center">
                <i class="fas fa-utensils text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">饮食</p>
            </div>
            <div class="text-center">
                <i class="fas fa-search text-purple-600 text-xl mb-1"></i>
                <p class="text-xs text-purple-600 font-medium">查询</p>
            </div>
            <div class="text-center">
                <i class="fas fa-chart-bar text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">健康</p>
            </div>
            <div class="text-center">
                <i class="fas fa-user text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">我的</p>
            </div>
        </div>
    </div>
</body>
</html>
