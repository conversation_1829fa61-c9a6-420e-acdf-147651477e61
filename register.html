<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .glass-effect { 
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-focus:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div class="min-h-screen flex flex-col justify-center px-6 py-8">
        <!-- 返回按钮 -->
        <div class="mb-4">
            <button class="text-white hover:text-purple-200">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
        </div>

        <!-- Logo区域 -->
        <div class="text-center mb-6">
            <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-lg">
                <i class="fas fa-heartbeat text-2xl text-purple-600"></i>
            </div>
            <h1 class="text-2xl font-bold text-white mb-1">创建账号</h1>
            <p class="text-purple-100 text-sm">开始您的健康饮食之旅</p>
        </div>

        <!-- 注册表单 -->
        <div class="glass-effect rounded-2xl p-6 shadow-xl">
            <form class="space-y-4">
                <!-- 手机号输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-phone text-gray-400"></i>
                        </div>
                        <input type="tel" 
                               class="input-focus block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500"
                               placeholder="请输入手机号">
                    </div>
                </div>

                <!-- 验证码输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">验证码</label>
                    <div class="flex space-x-3">
                        <div class="relative flex-1">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-shield-alt text-gray-400"></i>
                            </div>
                            <input type="text" 
                                   class="input-focus block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500"
                                   placeholder="请输入验证码">
                        </div>
                        <button type="button" 
                                class="px-4 py-3 bg-purple-100 text-purple-600 rounded-lg font-medium hover:bg-purple-200 transition-colors">
                            获取验证码
                        </button>
                    </div>
                </div>

                <!-- 密码输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">设置密码</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input type="password" 
                               class="input-focus block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500"
                               placeholder="请设置密码（6-20位）">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-eye text-gray-400 cursor-pointer"></i>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">密码需包含字母和数字，长度6-20位</p>
                </div>

                <!-- 确认密码 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">确认密码</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input type="password" 
                               class="input-focus block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500"
                               placeholder="请再次输入密码">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-eye text-gray-400 cursor-pointer"></i>
                        </div>
                    </div>
                </div>

                <!-- 邀请码（可选） -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">邀请码（可选）</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-gift text-gray-400"></i>
                        </div>
                        <input type="text" 
                               class="input-focus block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500"
                               placeholder="输入邀请码可获得额外福利">
                    </div>
                </div>

                <!-- 协议同意 -->
                <div class="flex items-start">
                    <input type="checkbox" class="mt-1 rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                    <span class="ml-2 text-sm text-gray-600">
                        我已阅读并同意
                        <a href="#" class="text-purple-600 hover:text-purple-500">《用户协议》</a>
                        和
                        <a href="#" class="text-purple-600 hover:text-purple-500">《隐私政策》</a>
                    </span>
                </div>

                <!-- 注册按钮 -->
                <button type="submit" 
                        class="btn-primary w-full py-3 px-4 rounded-lg text-white font-semibold shadow-lg">
                    立即注册
                </button>
            </form>

            <!-- 登录链接 -->
            <div class="mt-6 text-center">
                <span class="text-sm text-gray-600">已有账号？</span>
                <a href="#" class="text-sm text-purple-600 hover:text-purple-500 font-semibold">立即登录</a>
            </div>
        </div>
    </div>
</body>
</html>
