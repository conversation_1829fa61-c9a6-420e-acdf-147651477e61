<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .glass-effect { 
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-focus:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div class="min-h-screen flex flex-col justify-center px-6 py-12">
        <!-- Logo区域 -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 mx-auto mb-4 bg-white rounded-full flex items-center justify-center shadow-lg">
                <i class="fas fa-heartbeat text-3xl text-purple-600"></i>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">健康饮食</h1>
            <p class="text-purple-100">专业的慢性病饮食管理助手</p>
        </div>

        <!-- 登录表单 -->
        <div class="glass-effect rounded-2xl p-6 shadow-xl">
            <h2 class="text-2xl font-bold text-gray-800 text-center mb-6">欢迎回来</h2>
            
            <form class="space-y-4">
                <!-- 手机号输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-phone text-gray-400"></i>
                        </div>
                        <input type="tel" 
                               class="input-focus block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500"
                               placeholder="请输入手机号">
                    </div>
                </div>

                <!-- 密码输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input type="password" 
                               class="input-focus block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500"
                               placeholder="请输入密码">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-eye text-gray-400 cursor-pointer"></i>
                        </div>
                    </div>
                </div>

                <!-- 记住密码和忘记密码 -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                        <span class="ml-2 text-sm text-gray-600">记住密码</span>
                    </label>
                    <a href="#" class="text-sm text-purple-600 hover:text-purple-500">忘记密码？</a>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" 
                        class="btn-primary w-full py-3 px-4 rounded-lg text-white font-semibold shadow-lg">
                    登录
                </button>
            </form>

            <!-- 第三方登录 -->
            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">或使用以下方式登录</span>
                    </div>
                </div>

                <div class="mt-4 grid grid-cols-2 gap-3">
                    <button class="flex justify-center items-center px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50">
                        <i class="fab fa-weixin text-green-500 text-xl mr-2"></i>
                        <span class="text-sm text-gray-700">微信</span>
                    </button>
                    <button class="flex justify-center items-center px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50">
                        <i class="fab fa-apple text-black text-xl mr-2"></i>
                        <span class="text-sm text-gray-700">Apple ID</span>
                    </button>
                </div>
            </div>

            <!-- 注册链接 -->
            <div class="mt-6 text-center">
                <span class="text-sm text-gray-600">还没有账号？</span>
                <a href="#" class="text-sm text-purple-600 hover:text-purple-500 font-semibold">立即注册</a>
            </div>
        </div>

        <!-- 隐私协议 -->
        <div class="mt-6 text-center">
            <p class="text-xs text-purple-100">
                登录即表示您同意我们的
                <a href="#" class="underline">用户协议</a>
                和
                <a href="#" class="underline">隐私政策</a>
            </p>
        </div>
    </div>
</body>
</html>
