<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慢性病饮食管理App - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px;
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f5f5f7;
            min-height: 100vh;
        }
        .page-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
        }
        iframe {
            border: none;
            width: 100%;
            height: calc(100% - 44px);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="text-center py-8">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">慢性病饮食管理App</h1>
        <p class="text-xl text-gray-600">高保真原型设计 - iPhone 16 Pro 尺寸</p>
    </div>

    <div class="prototype-grid">
        <!-- 登录页面 -->
        <div class="phone-frame">
            <div class="page-title">登录页面</div>
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>健康饮食</span>
                    <span><i class="fas fa-battery-three-quarters"></i> <i class="fas fa-wifi"></i></span>
                </div>
                <iframe src="login.html"></iframe>
            </div>
        </div>

        <!-- 注册页面 -->
        <div class="phone-frame">
            <div class="page-title">注册页面</div>
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>健康饮食</span>
                    <span><i class="fas fa-battery-three-quarters"></i> <i class="fas fa-wifi"></i></span>
                </div>
                <iframe src="register.html"></iframe>
            </div>
        </div>

        <!-- 健康档案设置 -->
        <div class="phone-frame">
            <div class="page-title">健康档案设置</div>
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>健康饮食</span>
                    <span><i class="fas fa-battery-three-quarters"></i> <i class="fas fa-wifi"></i></span>
                </div>
                <iframe src="health-profile.html"></iframe>
            </div>
        </div>

        <!-- 首页 -->
        <div class="phone-frame">
            <div class="page-title">首页 - 健康概览</div>
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>健康饮食</span>
                    <span><i class="fas fa-battery-three-quarters"></i> <i class="fas fa-wifi"></i></span>
                </div>
                <iframe src="home.html"></iframe>
            </div>
        </div>

        <!-- 饮食计划 -->
        <div class="phone-frame">
            <div class="page-title">饮食计划</div>
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>健康饮食</span>
                    <span><i class="fas fa-battery-three-quarters"></i> <i class="fas fa-wifi"></i></span>
                </div>
                <iframe src="diet-plan.html"></iframe>
            </div>
        </div>

        <!-- 食物查询 -->
        <div class="phone-frame">
            <div class="page-title">食物查询</div>
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>健康饮食</span>
                    <span><i class="fas fa-battery-three-quarters"></i> <i class="fas fa-wifi"></i></span>
                </div>
                <iframe src="food-search.html"></iframe>
            </div>
        </div>

        <!-- 健康管理 -->
        <div class="phone-frame">
            <div class="page-title">健康管理</div>
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>健康饮食</span>
                    <span><i class="fas fa-battery-three-quarters"></i> <i class="fas fa-wifi"></i></span>
                </div>
                <iframe src="health-management.html"></iframe>
            </div>
        </div>

        <!-- 个人中心 -->
        <div class="phone-frame">
            <div class="page-title">个人中心</div>
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>健康饮食</span>
                    <span><i class="fas fa-battery-three-quarters"></i> <i class="fas fa-wifi"></i></span>
                </div>
                <iframe src="profile.html"></iframe>
            </div>
        </div>

        <!-- VIP订购页面 -->
        <div class="phone-frame">
            <div class="page-title">VIP会员订购</div>
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>健康饮食</span>
                    <span><i class="fas fa-battery-three-quarters"></i> <i class="fas fa-wifi"></i></span>
                </div>
                <iframe src="vip-subscription.html"></iframe>
            </div>
        </div>
    </div>
</body>
</html>
