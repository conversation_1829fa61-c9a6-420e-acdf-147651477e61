{"version": 3, "sources": ["../src/index.js"], "names": ["types", "plugins"], "mappings": ";;;;;;;;;AAAA;;AAEA;;AACA;;eAEe,wBAAU;AACvBA,EAAAA,KAAK,EAAE,CAACA,iBAAD,CADgB;AAEvBC,EAAAA,OAAO,EAAE,CAACA,mBAAD;AAFc,CAAV,C", "sourcesContent": ["import configure from '@jimp/custom';\n\nimport types from '@jimp/types';\nimport plugins from '@jimp/plugins';\n\nexport default configure({\n  types: [types],\n  plugins: [plugins]\n});\n"], "file": "index.js"}