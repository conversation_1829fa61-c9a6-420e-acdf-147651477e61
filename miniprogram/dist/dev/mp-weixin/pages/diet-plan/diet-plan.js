"use strict";const e=require("../../common/vendor.js"),t={name:"DietPlan",data:()=>({statusBarHeight:20,navBarHeight:64,currentDate:new Date,nutritionTargets:[{name:"热量",current:680,target:1800,percentage:38,color:"#3b82f6",colorClass:"blue"},{name:"蛋白质",current:28,target:75,percentage:38,color:"#22c55e",colorClass:"green"},{name:"脂肪",current:12,target:60,percentage:20,color:"#f59e0b",colorClass:"yellow"}],breakfastFoods:[{name:"燕麦粥",amount:"100g",calories:389,carbs:66,image:"/static/images/oatmeal.jpg"},{name:"蓝莓",amount:"50g",calories:29,carbs:7,image:"/static/images/blueberry.jpg"}],lunchRecommendations:[{name:"糙米饭 (100g)",calories:111},{name:"清蒸鲈鱼 (150g)",calories:125},{name:"菠菜豆腐汤 (200ml)",calories:45}]}),computed:{currentDateText(){return`${this.currentDate.getFullYear()}年${this.currentDate.getMonth()+1}月${this.currentDate.getDate()}日`},weekDays(){const e=[],t=new Date(this.currentDate),a=t.getDay(),n=new Date(t);n.setDate(t.getDate()-a+1);const r=["周一","周二","周三","周四","周五","周六","周日"];for(let s=0;s<7;s++){const t=new Date(n);t.setDate(n.getDate()+s),e.push({label:r[s],number:t.getDate(),date:new Date(t),isToday:this.isSameDay(t,this.currentDate)})}return e}},onLoad(){this.initPage()},methods:{initPage(){this.statusBarHeight=e.index.getStorageSync("statusBarHeight")||20,this.navBarHeight=e.index.getStorageSync("navBarHeight")||64},isSameDay:(e,t)=>e.getFullYear()===t.getFullYear()&&e.getMonth()===t.getMonth()&&e.getDate()===t.getDate(),previousDay(){const e=new Date(this.currentDate);e.setDate(e.getDate()-1),this.currentDate=e},nextDay(){const e=new Date(this.currentDate);e.setDate(e.getDate()+1),this.currentDate=e},selectDate(e){this.currentDate=e.date},addMeal(t){e.index.showToast({title:"添加"+("lunch"===t?"午餐":"晚餐"),icon:"none"})},adoptRecommendation(){e.index.showModal({title:"采用推荐菜单",content:"确定要采用AI推荐的午餐菜单吗？",success:t=>{t.confirm&&e.index.showToast({title:"已添加到午餐计划",icon:"success"})}})}}};const a=e._export_sfc(t,[["render",function(t,a,n,r,s,o){return{a:e.o((...e)=>o.previousDay&&o.previousDay(...e)),b:e.t(o.currentDateText),c:e.o((...e)=>o.nextDay&&o.nextDay(...e)),d:e.f(o.weekDays,(t,a,n)=>({a:e.t(t.label),b:e.t(t.number),c:a,d:t.isToday?1:"",e:e.o(e=>o.selectDate(t),a)})),e:s.statusBarHeight+"px",f:e.f(s.nutritionTargets,(t,a,n)=>({a:e.n(t.colorClass),b:`conic-gradient(${t.color} ${3.6*t.percentage}deg, #e5e7eb 0deg)`,c:e.t(t.percentage),d:e.t(t.name),e:e.t(t.current),f:e.t(t.target),g:a})),g:e.f(s.breakfastFoods,(t,a,n)=>({a:t.image,b:e.t(t.name),c:e.t(t.amount),d:e.t(t.calories),e:e.t(t.carbs),f:a})),h:e.o(e=>o.addMeal("lunch")),i:e.f(s.lunchRecommendations,(t,a,n)=>({a:e.t(t.name),b:e.t(t.calories),c:a})),j:e.o((...e)=>o.adoptRecommendation&&o.adoptRecommendation(...e)),k:e.o(e=>o.addMeal("dinner")),l:s.navBarHeight+"px"}}],["__scopeId","data-v-9ec23dc3"],["__file","/Users/<USER>/Desktop/历史脚步/test-app/miniprogram/src/pages/diet-plan/diet-plan.vue"]]);wx.createPage(a);
