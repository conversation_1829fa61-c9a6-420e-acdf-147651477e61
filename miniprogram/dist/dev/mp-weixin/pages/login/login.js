"use strict";const e=require("../../common/vendor.js"),o={name:"Login",data:()=>({loginForm:{phone:"",password:""},showPassword:!1,rememberPassword:!1,isLoading:!1}),computed:{canLogin(){return 11===this.loginForm.phone.length&&this.loginForm.password.length>=6&&!this.isLoading}},onLoad(){this.loadSavedCredentials()},methods:{loadSavedCredentials(){const o=e.index.getStorageSync("savedPhone"),n=e.index.getStorageSync("savedPassword");e.index.getStorageSync("rememberPassword")&&(this.loginForm.phone=o||"",this.loginForm.password=n||"",this.rememberPassword=!0)},togglePassword(){this.showPassword=!this.showPassword},handleRememberChange(e){this.rememberPassword=e.detail.value.length>0},handleLogin(){return o=this,n=null,s=function*(){if(this.canLogin&&this.validateForm()){this.isLoading=!0;try{yield this.loginRequest(),this.rememberPassword?(e.index.setStorageSync("savedPhone",this.loginForm.phone),e.index.setStorageSync("savedPassword",this.loginForm.password),e.index.setStorageSync("rememberPassword",!0)):(e.index.removeStorageSync("savedPhone"),e.index.removeStorageSync("savedPassword"),e.index.removeStorageSync("rememberPassword")),e.index.setStorageSync("token","mock_token_"+Date.now()),e.index.setStorageSync("userData",{name:"张先生",phone:this.loginForm.phone,avatar:"/static/images/default-avatar.png"}),e.index.showToast({title:"登录成功",icon:"success"}),setTimeout(()=>{e.index.switchTab({url:"/pages/index/index"})},1500)}catch(o){e.index.showToast({title:o.message||"登录失败",icon:"none"})}finally{this.isLoading=!1}}},new Promise((e,i)=>{var t=e=>{try{r(s.next(e))}catch(o){i(o)}},a=e=>{try{r(s.throw(e))}catch(o){i(o)}},r=o=>o.done?e(o.value):Promise.resolve(o.value).then(t,a);r((s=s.apply(o,n)).next())});var o,n,s},validateForm(){return/^1[3-9]\d{9}$/.test(this.loginForm.phone)?!(this.loginForm.password.length<6)||(e.index.showToast({title:"密码长度不能少于6位",icon:"none"}),!1):(e.index.showToast({title:"请输入正确的手机号",icon:"none"}),!1)},loginRequest(){return new Promise((e,o)=>{setTimeout(()=>{"13800138000"===this.loginForm.phone&&"123456"===this.loginForm.password?e():o(new Error("手机号或密码错误"))},2e3)})},handleForgotPassword(){e.index.showToast({title:"功能开发中",icon:"none"})},handleWechatLogin(){e.index.showToast({title:"微信登录开发中",icon:"none"})},handleAppleLogin(){e.index.showToast({title:"Apple登录开发中",icon:"none"})},goToRegister(){e.index.navigateTo({url:"/pages/register/register"})},viewUserAgreement(){e.index.showToast({title:"用户协议",icon:"none"})},viewPrivacyPolicy(){e.index.showToast({title:"隐私政策",icon:"none"})}}};const n=e._export_sfc(o,[["render",function(o,n,s,i,t,a){return{a:t.loginForm.phone,b:e.o(e=>t.loginForm.phone=e.detail.value),c:t.showPassword?"text":"password",d:t.loginForm.password,e:e.o(e=>t.loginForm.password=e.detail.value),f:e.n(t.showPassword?"icon-eye-slash":"icon-eye"),g:e.o((...e)=>a.togglePassword&&a.togglePassword(...e)),h:t.rememberPassword,i:e.o((...e)=>a.handleRememberChange&&a.handleRememberChange(...e)),j:e.o((...e)=>a.handleForgotPassword&&a.handleForgotPassword(...e)),k:e.t(t.isLoading?"登录中...":"登录"),l:a.canLogin?"":1,m:!a.canLogin,n:e.o((...e)=>a.handleLogin&&a.handleLogin(...e)),o:e.o((...e)=>a.handleLogin&&a.handleLogin(...e)),p:e.o((...e)=>a.handleWechatLogin&&a.handleWechatLogin(...e)),q:e.o((...e)=>a.handleAppleLogin&&a.handleAppleLogin(...e)),r:e.o((...e)=>a.goToRegister&&a.goToRegister(...e)),s:e.o((...e)=>a.viewUserAgreement&&a.viewUserAgreement(...e)),t:e.o((...e)=>a.viewPrivacyPolicy&&a.viewPrivacyPolicy(...e))}}],["__scopeId","data-v-cdfe2409"],["__file","/Users/<USER>/Desktop/历史脚步/test-app/miniprogram/src/pages/login/login.vue"]]);wx.createPage(n);
