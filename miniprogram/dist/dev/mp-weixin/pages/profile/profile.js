"use strict";var e=Object.defineProperty,t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,o=(t,n,i)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[n]=i,a=(e,a)=>{for(var c in a||(a={}))n.call(a,c)&&o(e,c,a[c]);if(t)for(var c of t(a))i.call(a,c)&&o(e,c,a[c]);return e};const c=require("../../common/vendor.js"),r={name:"Profile",data:()=>({statusBarHeight:20,headerHeight:300,userInfo:{name:"张先生",condition:"糖尿病患者",age:35,avatar:"/static/images/default-avatar.png"},vipExpireDate:"2024-12-31",healthOverview:[{label:"管理天数",value:"32"},{label:"健康评分",value:"85"},{label:"体重变化(kg)",value:"-2.3"}],functionMenus:[{title:"个人资料",desc:"编辑基本信息和健康档案",icon:"icon-user-edit",iconBg:"bg-blue",action:"profile"},{title:"健康报告",desc:"查看详细的健康分析报告",icon:"icon-chart-line",iconBg:"bg-green",action:"report"},{title:"饮食偏好",desc:"设置口味偏好和忌口食物",icon:"icon-utensils",iconBg:"bg-purple",action:"preference"},{title:"提醒设置",desc:"用药提醒、饮食提醒等",icon:"icon-bell",iconBg:"bg-red",action:"reminder"}],dataMenus:[{title:"数据同步",desc:"同步到云端，多设备访问",icon:"icon-cloud",iconBg:"bg-blue",extra:"已同步",extraClass:"text-success",action:"sync"},{title:"数据导出",desc:"导出健康数据和饮食记录",icon:"icon-download",iconBg:"bg-orange",action:"export"},{title:"隐私设置",desc:"管理数据隐私和权限",icon:"icon-shield",iconBg:"bg-gray",action:"privacy"}],helpMenus:[{title:"常见问题",icon:"icon-question",iconBg:"bg-green",action:"faq"},{title:"联系客服",icon:"icon-headset",iconBg:"bg-blue",action:"contact"},{title:"评价应用",icon:"icon-star",iconBg:"bg-purple",action:"rate"},{title:"关于我们",icon:"icon-info",iconBg:"bg-gray",action:"about"}]}),onLoad(){this.initPage()},onShow(){this.loadUserData()},methods:{initPage(){this.statusBarHeight=c.index.getStorageSync("statusBarHeight")||20},loadUserData(){const e=c.index.getStorageSync("userData");e&&(this.userInfo=a(a({},this.userInfo),e))},goToSettings(){c.index.showToast({title:"设置功能开发中",icon:"none"})},renewVip(){c.index.navigateTo({url:"/pages/vip-subscription/vip-subscription"})},handleMenuClick(e){switch(e.action){case"profile":c.index.navigateTo({url:"/pages/health-profile/health-profile"});break;case"report":c.index.switchTab({url:"/pages/health-management/health-management"});break;default:c.index.showToast({title:`${e.title}功能开发中`,icon:"none"})}},handleLogout(){c.index.showModal({title:"退出登录",content:"确定要退出登录吗？",success:e=>{e.confirm&&(c.index.removeStorageSync("token"),c.index.removeStorageSync("userData"),c.index.showToast({title:"已退出登录",icon:"success"}),setTimeout(()=>{c.index.reLaunch({url:"/pages/login/login"})},1500))}})}}};const s=c._export_sfc(r,[["render",function(e,t,n,i,o,a){return{a:o.userInfo.avatar,b:c.t(o.userInfo.name),c:c.t(o.userInfo.condition),d:c.t(o.userInfo.age),e:c.o((...e)=>a.goToSettings&&a.goToSettings(...e)),f:c.f(o.healthOverview,(e,t,n)=>({a:c.t(e.value),b:c.t(e.label),c:t})),g:o.statusBarHeight+"px",h:c.t(o.vipExpireDate),i:c.o((...e)=>a.renewVip&&a.renewVip(...e)),j:c.f(o.functionMenus,(e,t,n)=>({a:c.n(e.icon),b:c.n(e.iconBg),c:c.t(e.title),d:c.t(e.desc),e:t,f:c.o(t=>a.handleMenuClick(e),t)})),k:c.f(o.dataMenus,(e,t,n)=>c.e({a:c.n(e.icon),b:c.n(e.iconBg),c:c.t(e.title),d:c.t(e.desc),e:e.extra},e.extra?{f:c.t(e.extra),g:c.n(e.extraClass)}:{},{h:t,i:c.o(t=>a.handleMenuClick(e),t)})),l:c.f(o.helpMenus,(e,t,n)=>({a:c.n(e.icon),b:c.n(e.iconBg),c:c.t(e.title),d:t,e:c.o(t=>a.handleMenuClick(e),t)})),m:c.o((...e)=>a.handleLogout&&a.handleLogout(...e)),n:o.headerHeight+"px"}}],["__scopeId","data-v-04d37cba"],["__file","/Users/<USER>/Desktop/历史脚步/test-app/miniprogram/src/pages/profile/profile.vue"]]);wx.createPage(s);
