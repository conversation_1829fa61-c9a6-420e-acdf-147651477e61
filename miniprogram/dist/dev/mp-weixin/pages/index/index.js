"use strict";const e=require("../../common/vendor.js"),t={name:"Index",data:()=>({statusBarHeight:20,navBarHeight:64,managementDays:32,healthScore:85,scoreChange:5,reminderCount:2,appointmentCount:1,userInfo:{name:"张先生"},mealStatus:[{name:"早餐",status:"已记录",icon:"icon-check",statusClass:"status-completed"},{name:"午餐",status:"待记录",icon:"icon-clock",statusClass:"status-pending"},{name:"晚餐",status:"未开始",icon:"icon-plus",statusClass:"status-inactive"}],nutritionProgress:[{name:"热量",current:680,target:1800,unit:"kcal",percentage:38,colorClass:"progress-blue"},{name:"碳水化合物",current:85,target:225,unit:"g",percentage:38,colorClass:"progress-green"}],quickActions:[{name:"食物查询",icon:"icon-search",bgClass:"bg-blue",action:"search"},{name:"记录饮食",icon:"icon-plus",bgClass:"bg-green",action:"record"},{name:"用药提醒",icon:"icon-pills",bgClass:"bg-purple",action:"medicine"},{name:"健康报告",icon:"icon-chart",bgClass:"bg-red",action:"report"}],healthTrends:[{name:"血糖",value:"6.8",unit:"mmol/L",trendIcon:"icon-arrow-up",trendColor:"#ef4444"},{name:"体重",value:"68.2",unit:"kg",trendIcon:"icon-arrow-down",trendColor:"#22c55e"}]}),computed:{greetingText(){const e=(new Date).getHours();return e<12?"早上好":e<18?"下午好":"晚上好"},hasReminders(){return this.reminderCount>0||this.appointmentCount>0}},onLoad(){this.initPage()},onShow(){this.refreshData()},methods:{initPage(){this.statusBarHeight=e.index.getStorageSync("statusBarHeight")||20,this.navBarHeight=e.index.getStorageSync("navBarHeight")||64,this.loadUserData()},loadUserData(){const t=e.index.getStorageSync("userData");t&&(this.userInfo=t)},refreshData(){this.loadHealthData(),this.loadDietData()},loadHealthData(){},loadDietData(){},handleNotification(){e.index.showToast({title:"暂无新通知",icon:"none"})},viewReminders(){e.index.navigateTo({url:"/pages/health-management/health-management"})},viewDietDetail(){e.index.switchTab({url:"/pages/diet-plan/diet-plan"})},viewHealthTrend(){e.index.switchTab({url:"/pages/health-management/health-management"})},handleQuickAction(t){switch(t.action){case"search":e.index.switchTab({url:"/pages/food-search/food-search"});break;case"record":e.index.switchTab({url:"/pages/diet-plan/diet-plan"});break;case"medicine":case"report":e.index.switchTab({url:"/pages/health-management/health-management"});break;default:e.index.showToast({title:"功能开发中",icon:"none"})}}}};const a=e._export_sfc(t,[["render",function(t,a,n,i,s,o){return e.e({a:e.t(o.greetingText),b:e.t(s.userInfo.name||"用户"),c:e.t(s.managementDays),d:e.o((...e)=>o.handleNotification&&o.handleNotification(...e)),e:e.t(s.healthScore),f:e.t(s.scoreChange),g:s.statusBarHeight+"px",h:o.hasReminders},o.hasReminders?{i:e.t(s.reminderCount),j:e.t(s.appointmentCount),k:e.o((...e)=>o.viewReminders&&o.viewReminders(...e))}:{},{l:e.o((...e)=>o.viewDietDetail&&o.viewDietDetail(...e)),m:e.f(s.mealStatus,(t,a,n)=>({a:e.n(t.icon),b:e.n(t.statusClass),c:e.t(t.name),d:e.t(t.status),e:a})),n:e.f(s.nutritionProgress,(t,a,n)=>({a:e.t(t.name),b:e.t(t.current),c:e.t(t.target),d:e.t(t.unit),e:e.n(t.colorClass),f:t.percentage+"%",g:a})),o:e.f(s.quickActions,(t,a,n)=>({a:e.n(t.icon),b:e.n(t.bgClass),c:e.t(t.name),d:a,e:e.o(e=>o.handleQuickAction(t),a)})),p:e.o((...e)=>o.viewHealthTrend&&o.viewHealthTrend(...e)),q:e.f(s.healthTrends,(t,a,n)=>({a:e.t(t.name),b:e.n(t.trendIcon),c:t.trendColor,d:e.t(t.value),e:e.t(t.unit),f:a})),r:s.navBarHeight+"px"})}],["__scopeId","data-v-83a5a03c"],["__file","/Users/<USER>/Desktop/历史脚步/test-app/miniprogram/src/pages/index/index.vue"]]);wx.createPage(a);
