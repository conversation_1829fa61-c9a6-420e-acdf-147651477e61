"use strict";function e(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function t(e){let n="";if(y(e))n=e;else if(h(e))for(let o=0;o<e.length;o++){const r=t(e[o]);r&&(n+=r+" ")}else if(_(e))for(const t in e)e[t]&&(n+=t+" ");return n.trim()}const n=(e,t)=>t&&t.__v_isRef?n(e,t.value):g(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n])=>(e[`${t} =>`]=n,e),{})}:m(t)?{[`Set(${t.size})`]:[...t.values()]}:!_(t)||h(t)||k(t)?t:String(t),o=Object.freeze({}),r=Object.freeze([]),i=()=>{},s=()=>!1,c=/^on[^a-z]/,a=e=>c.test(e),u=e=>e.startsWith("onUpdate:"),l=Object.assign,p=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},f=Object.prototype.hasOwnProperty,d=(e,t)=>f.call(e,t),h=Array.isArray,g=e=>"[object Map]"===$(e),m=e=>"[object Set]"===$(e),v=e=>"function"==typeof e,y=e=>"string"==typeof e,b=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,x=e=>_(e)&&v(e.then)&&v(e.catch),w=Object.prototype.toString,$=e=>w.call(e),O=e=>$(e).slice(8,-1),k=e=>"[object Object]"===$(e),S=e=>y(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,C=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),P=e("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),j=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},E=/-(\w)/g,A=j(e=>e.replace(E,(e,t)=>t?t.toUpperCase():"")),I=/\B([A-Z])/g,T=j(e=>e.replace(I,"-$1").toLowerCase()),M=j(e=>e.charAt(0).toUpperCase()+e.slice(1)),R=j(e=>e?`on${M(e)}`:""),V=(e,t)=>!Object.is(e,t),L=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},D=e=>{const t=parseFloat(e);return isNaN(t)?e:t},N="onShow",H="onHide",B="onLaunch",U="onError",z="onThemeChange",W="onPageNotFound",K="onUnhandledRejection",F="onLoad",q="onReady",J="onUnload",G="onInit",Y="onSaveExitState",Z="onResize",Q="onBackPress",X="onPageScroll",ee="onTabItemTap",te="onReachBottom",ne="onPullDownRefresh",oe="onShareTimeline",re="onAddToFavorites",ie="onShareAppMessage",se="onNavigationBarButtonTap",ce="onNavigationBarSearchInputClicked",ae="onNavigationBarSearchInputChanged",ue="onNavigationBarSearchInputConfirmed",le="onNavigationBarSearchInputFocusChanged",pe=/:/g;function fe(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function de(e,t){if(!y(t))return;const n=(t=t.replace(/\[(\d+)\]/g,".$1")).split(".");let o=n[0];return e||(e={}),1===n.length?e[o]:de(e[o],n.slice(1).join("."))}function he(e){let t={};return k(e)&&Object.keys(e).sort().forEach(n=>{const o=n;t[o]=e[o]}),Object.keys(t)?t:e}const ge=encodeURIComponent;function me(e,t=ge){const n=e?Object.keys(e).map(n=>{let o=e[n];return void 0===typeof o||null===o?o="":k(o)&&(o=JSON.stringify(o)),t(n)+"="+t(o)}).filter(e=>e.length>0).join("&"):null;return n?`?${n}`:""}const ve=[G,F,N,H,J,Q,X,ee,te,ne,oe,ie,re,Y,se,ce,ae,ue,le];const ye=[N,H,B,U,z,W,K,G,F,q,J,Z,Q,X,ee,te,ne,oe,re,ie,Y,se,ce,ae,ue,le],be=(()=>({onPageScroll:1,onShareAppMessage:2,onShareTimeline:4}))();function _e(e,t,n=!0){return!(n&&!v(t))&&(ye.indexOf(e)>-1||0===e.indexOf("on"))}let xe;const we=[];const $e=fe((e,t)=>{if(v(e._component.onError))return t(e)}),Oe=function(){};Oe.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t)for(var i=0,s=o.length;i<s;i++)o[i].fn!==t&&o[i].fn._!==t&&r.push(o[i]);return r.length?n[e]=r:delete n[e],this}};var ke=Oe;const Se="zh-Hans",Ce="zh-Hant",Pe="en";function je(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return Se;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?Se:e.indexOf("-hant")>-1?Ce:(n=e,["-tw","-hk","-mo","-cht"].find(e=>-1!==n.indexOf(e))?Ce:Se);var n;let o=[Pe,"fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find(t=>0===e.indexOf(t))}(e,o);return r||void 0}function Ee(e,t){}function Ae(e,t,n,o){o||(o=Ee);for(const r in n){const i=Ie(r,t[r],n[r],!d(t,r));y(i)&&o(e,i)}}function Ie(e,t,n,o){k(n)||(n={type:n});const{type:r,required:i,validator:s}=n;if(i&&o)return'Missing required args: "'+e+'"';if(null!=t||i){if(null!=r){let n=!1;const o=h(r)?r:[r],i=[];for(let e=0;e<o.length&&!n;e++){const{valid:r,expectedType:s}=Me(t,o[e]);i.push(s||""),n=r}if(!n)return function(e,t,n){let o=`Invalid args: type check failed for args "${e}". Expected ${n.map(M).join(", ")}`;const r=n[0],i=O(t),s=Re(t,r),c=Re(t,i);1===n.length&&Ve(r)&&!function(...e){return e.some(e=>"boolean"===e.toLowerCase())}(r,i)&&(o+=` with value ${s}`);o+=`, got ${i} `,Ve(i)&&(o+=`with value ${c}.`);return o}(e,t,i)}return s?s(t):void 0}}const Te=e("String,Number,Boolean,Function,Symbol");function Me(e,t){let n;const o=function(e){const t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:""}(t);if(Te(o)){const r=typeof e;n=r===o.toLowerCase(),n||"object"!==r||(n=e instanceof t)}else n="Object"===o?_(e):"Array"===o?h(e):e instanceof t;return{valid:n,expectedType:o}}function Re(e,t){return"String"===t?`"${e}"`:"Number"===t?`${Number(e)}`:`${e}`}function Ve(e){return["string","number","boolean"].some(t=>e.toLowerCase()===t)}function Le(e){return function(){try{return e.apply(e,arguments)}catch(t){}}}let De=1;const Ne={};function He(e,t,n){if("number"==typeof e){const o=Ne[e];if(o)return o.keepAlive||delete Ne[e],o.callback(t,n)}return t}const Be="success",Ue="fail",ze="complete";function We(e,t={},{beforeAll:n,beforeSuccess:o}={}){k(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];v(o)&&(t[n]=Le(o),delete e[n])}return t}(t),c=v(r),a=v(i),u=v(s),l=De++;return function(e,t,n,o=!1){Ne[e]={name:t,keepAlive:o,callback:n}}(l,e,l=>{(l=l||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(l.errMsg,e),v(n)&&n(l),l.errMsg===e+":ok"?(v(o)&&o(l,t),c&&r(l)):a&&i(l),u&&s(l)}),l}const Ke="success",Fe="fail",qe="complete",Je={},Ge={};function Ye(e,t){return function(n){return e(n,t)||n}}function Ze(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Ye(i,n));else{const e=i(t,n);if(x(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Qe(e,t={}){return[Ke,Fe,qe].forEach(n=>{const o=e[n];if(!h(o))return;const r=t[n];t[n]=function(e){Ze(o,e,t).then(e=>v(r)&&r(e)||e)}}),t}function Xe(e,t){const n=[];h(Je.returnValue)&&n.push(...Je.returnValue);const o=Ge[e];return o&&h(o.returnValue)&&n.push(...o.returnValue),n.forEach(e=>{t=e(t)||t}),t}function et(e){const t=Object.create(null);Object.keys(Je).forEach(e=>{"returnValue"!==e&&(t[e]=Je[e].slice())});const n=Ge[e];return n&&Object.keys(n).forEach(e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))}),t}function tt(e,t,n,o){const r=et(e);if(r&&Object.keys(r).length){if(h(r.invoke)){return Ze(r.invoke,n).then(n=>t(Qe(et(e),n),...o))}return t(Qe(r,n),...o)}return t(n,...o)}function nt(e,t){return(n={},...o)=>function(e){return!(!k(e)||![Be,Ue,ze].find(t=>v(e[t])))}(n)?Xe(e,tt(e,t,n,o)):Xe(e,new Promise((r,i)=>{tt(e,t,l(n,{success:r,fail:i}),o)}))}function ot(e,t,n,o){return He(e,l({errMsg:t+":fail"+(n?" "+n:"")},o))}function rt(e,t,n,o){if(function(e,t,n,o){if(!n)return;if(!h(n))return Ae(e,t[0]||Object.create(null),n,o);const r=n.length,i=t.length;for(let s=0;s<r;s++){const r=n[s],c=Object.create(null);i>s&&(c[r.name]=t[s]),Ae(e,c,{[r.name]:r},o)}}(e,t,n),o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(y(e))return e}const r=function(e,t){const n=e[0];if(!t||!k(t.formatArgs)&&k(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(v(s)){const o=s(e[0][t],n);if(y(o))return o}else d(n,t)||(n[t]=s)}}(t,o);if(r)return r}function it(e,t,n,o){return r=>{const i=We(e,r,o),s=rt(e,[r],n,o);return s?ot(i,e,s):t(r,{resolve:t=>function(e,t,n){return He(e,l(n||{},{errMsg:t+":ok"}))}(i,e,t),reject:(t,n)=>ot(i,e,function(e){return!e||y(e)?e:e.stack?e.message:e}(t),n)})}}function st(e,t,n,o){return function(e,t,n,o){return(...r)=>{const i=rt(e,r,n,o);if(i)throw new Error(i);return t.apply(null,r)}}(e,t,n,o)}let ct=!1,at=0,ut=0;function lt(){const{platform:e,pixelRatio:t,windowWidth:n}=wx.getSystemInfoSync();at=n,ut=t,ct="ios"===e}const pt=st("upx2px",(e,t)=>{if(0===at&&lt(),0===(e=Number(e)))return 0;let n=e/750*(t||at);return n<0&&(n=-n),n=Math.floor(n+1e-4),0===n&&(n=1!==ut&&ct?.5:1),e<0?-n:n},[{name:"upx",type:[Number,String],required:!0}]),ft=[{name:"method",type:[String,Object],required:!0}],dt=ft;function ht(e,t){Object.keys(t).forEach(n=>{v(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):h(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))})}function gt(e,t){e&&t&&Object.keys(t).forEach(n=>{const o=e[n],r=t[n];h(o)&&v(r)&&p(o,r)})}const mt=st("addInterceptor",(e,t)=>{y(e)&&k(t)?ht(Ge[e]||(Ge[e]={}),t):k(e)&&ht(Je,e)},ft),vt=st("removeInterceptor",(e,t)=>{y(e)?k(t)?gt(Ge[e],t):delete Ge[e]:k(e)&&gt(Je,e)},dt),yt=[{name:"event",type:String,required:!0},{name:"callback",type:Function,required:!0}],bt=yt,_t=[{name:"event",type:[String,Array]},{name:"callback",type:Function}],xt=[{name:"event",type:String,required:!0}],wt=new ke,$t=st("$on",(e,t)=>(wt.on(e,t),()=>wt.off(e,t)),yt),Ot=st("$once",(e,t)=>(wt.once(e,t),()=>wt.off(e,t)),bt),kt=st("$off",(e,t)=>{e?(h(e)||(e=[e]),e.forEach(e=>wt.off(e,t))):wt.e={}},_t),St=st("$emit",(e,...t)=>{wt.emit(e,...t)},xt);let Ct,Pt,jt;function Et(e){try{return JSON.parse(e)}catch(t){}return e}const At=[];function It(e,t){At.forEach(n=>{n(e,t)}),At.length=0}const Tt=nt(Mt="getPushClientId",function(e,t,n,o){return it(e,t,n,o)}(Mt,(e,{resolve:t,reject:n})=>{Promise.resolve().then(()=>{void 0===jt&&(jt=!1,Ct="",Pt="uniPush is not enabled"),At.push((e,o)=>{e?t({cid:e}):n(o)}),void 0!==Ct&&It(Ct,Pt)})},Rt,Vt));var Mt,Rt,Vt;const Lt=[],Dt=/^\$|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/,Nt=/^create|Manager$/,Ht=["createBLEConnection"],Bt=["createBLEConnection"],Ut=/^on|^off/;function zt(e){return Nt.test(e)&&-1===Ht.indexOf(e)}function Wt(e){return Dt.test(e)&&-1===Bt.indexOf(e)}function Kt(e){return!(zt(e)||Wt(e)||function(e){return Ut.test(e)&&"onPush"!==e}(e))}function Ft(e,t){return Kt(e)&&v(t)?function(n={},...o){return v(n.success)||v(n.fail)||v(n.complete)?Xe(e,tt(e,t,n,o)):Xe(e,new Promise((r,i)=>{tt(e,t,l({},n,{success:r,fail:i}),o)}))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then(n=>t.resolve(e&&e()).then(()=>n),n=>t.resolve(e&&e()).then(()=>{throw n}))});const qt=["success","fail","cancel","complete"];const Jt=()=>{const e=v(getApp)&&getApp({allowDefault:!0});return e&&e.$vm?e.$vm.$locale:je(wx.getSystemInfoSync().language)||Pe},Gt=[];"undefined"!=typeof global&&(global.getLocale=Jt);const Yt="__DC_STAT_UUID";let Zt;function Qt(e=wx){return function(t,n){Zt=Zt||e.getStorageSync(Yt),Zt||(Zt=Date.now()+""+Math.floor(1e7*Math.random()),wx.setStorage({key:Yt,data:Zt})),n.deviceId=Zt}}function Xt(e,t){if(e.safeArea){const n=e.safeArea;t.safeAreaInsets={top:n.top,left:n.left,right:e.windowWidth-n.right,bottom:e.screenHeight-n.bottom}}}function en(e,t){let n=e.deviceType||"phone";{const e={ipad:"pad",windows:"pc",mac:"pc"},o=Object.keys(e),r=t.toLocaleLowerCase();for(let t=0;t<o.length;t++){const i=o[t];if(-1!==r.indexOf(i)){n=e[i];break}}}return n}function tn(e){let t=e;return t&&(t=t.toLocaleLowerCase()),t}function nn(e){return Jt?Jt():e}function on(e){let t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}const rn={returnValue:(e,t)=>{Xt(e,t),Qt()(e,t),function(e,t){const{brand:n="",model:o="",system:r="",language:i="",theme:s,version:c,platform:a,fontSizeSetting:u,SDKVersion:p,pixelRatio:f,deviceOrientation:d}=e;let h="",g="";h=r.split(" ")[0]||"",g=r.split(" ")[1]||"";let m=c,v=en(e,o),y=tn(n),b=on(e),_=d,x=f,w=p;const $=i.replace(/_/g,"-"),O={appId:"__UNI__HEALTH_DIET",appName:"健康饮食管理",appVersion:"1.0.0",appVersionCode:"100",appLanguage:nn($),uniCompileVersion:"3.8.12",uniRuntimeVersion:"3.8.12",uniPlatform:"mp-weixin",deviceBrand:y,deviceModel:o,deviceType:v,devicePixelRatio:x,deviceOrientation:_,osName:h.toLocaleLowerCase(),osVersion:g,hostTheme:s,hostVersion:m,hostLanguage:$,hostName:b,hostSDKVersion:w,hostFontSizeSetting:u,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0};l(t,O)}(e,t)}},sn=rn,cn={args(e,t){let n=parseInt(e.current);if(isNaN(n))return;const o=e.urls;if(!h(o))return;const r=o.length;return r?(n<0?n=0:n>=r&&(n=r-1),n>0?(t.current=o[n],t.urls=o.filter((e,t)=>!(t<n)||e!==o[n])):t.current=o[0],{indicator:!1,loop:!1}):void 0}},an={args(e,t){t.alertText=e.title}},un={returnValue:(e,t)=>{const{brand:n,model:o}=e;let r=en(e,o),i=tn(n);Qt()(e,t),t=he(l(t,{deviceType:r,deviceBrand:i,deviceModel:o}))}},ln={returnValue:(e,t)=>{const{version:n,language:o,SDKVersion:r,theme:i}=e;let s=on(e),c=o.replace(/_/g,"-");t=he(l(t,{hostVersion:n,hostLanguage:c,hostName:s,hostSDKVersion:r,hostTheme:i,appId:"__UNI__HEALTH_DIET",appName:"健康饮食管理",appVersion:"1.0.0",appVersionCode:"100",appLanguage:nn(c)}))}},pn={returnValue:(e,t)=>{Xt(e,t),t=he(l(t,{windowTop:0,windowBottom:0}))}},fn={$on:$t,$off:kt,$once:Ot,$emit:St,upx2px:pt,interceptors:{},addInterceptor:mt,removeInterceptor:vt,onCreateVueApp:function(e){if(xe)return e(xe);we.push(e)},invokeCreateVueAppHook:function(e){xe=e,we.forEach(t=>t(e))},getLocale:Jt,setLocale:e=>{const t=v(getApp)&&getApp();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,Gt.forEach(t=>t({locale:e})),!0)},onLocaleChange:e=>{-1===Gt.indexOf(e)&&Gt.push(e)},getPushClientId:Tt,onPushMessage:e=>{-1===Lt.indexOf(e)&&Lt.push(e)},offPushMessage:e=>{if(e){const t=Lt.indexOf(e);t>-1&&Lt.splice(t,1)}else Lt.length=0},invokePushCallback:function(e){if("enabled"===e.type)jt=!0;else if("clientId"===e.type)Ct=e.cid,Pt=e.errMsg,It(Ct,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:Et(e.message)};for(let e=0;e<Lt.length;e++){if((0,Lt[e])(t),t.stopped)break}}else"click"===e.type&&Lt.forEach(t=>{t({type:"click",data:Et(e.message)})})}};const dn=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],hn=["lanDebug","router","worklet"],gn=wx.getLaunchOptionsSync?wx.getLaunchOptionsSync():null;function mn(e){return(!gn||1154!==gn.scene||!hn.includes(e))&&(dn.indexOf(e)>-1||"function"==typeof wx[e])}function vn(){const e={};for(const t in wx)mn(t)&&(e[t]=wx[t]);return"undefined"!=typeof globalThis&&"undefined"==typeof requireMiniProgram&&(globalThis.wx=e),e}const yn=["__route__","__wxExparserNodeId__","__wxWebviewId__"],bn=(_n={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]},function({service:e,success:t,fail:n,complete:o}){let r;_n[e]?(r={errMsg:"getProvider:ok",service:e,provider:_n[e]},v(t)&&t(r)):(r={errMsg:"getProvider:fail:服务["+e+"]不存在"},v(n)&&n(r)),v(o)&&o(r)});var _n;const xn=vn();let wn=xn.getAppBaseInfo&&xn.getAppBaseInfo();wn||(wn=xn.getSystemInfoSync());const $n=wn?wn.host:null,On=$n&&"SAAASDK"===$n.env?xn.miniapp.shareVideoMessage:xn.shareVideoMessage;var kn=Object.freeze({__proto__:null,createSelectorQuery:function(){const e=xn.createSelectorQuery(),t=e.in;return e.in=function(e){return t.call(this,function(e){const t=Object.create(null);return yn.forEach(n=>{t[n]=e[n]}),t}(e))},e},getProvider:bn,shareVideoMessage:On});const Sn={args(e,t){e.compressedHeight&&!t.compressHeight&&(t.compressHeight=e.compressedHeight),e.compressedWidth&&!t.compressWidth&&(t.compressWidth=e.compressedWidth)}};var Cn=function(e,t,n=wx){const o=function(e){function t(e,t,n){return function(r){return t(o(e,r,n))}}function n(e,n,o={},r={},i=!1){if(k(n)){const s=!0===i?n:{};v(o)&&(o=o(n,s)||{});for(const c in n)if(d(o,c)){let e=o[c];v(e)&&(e=e(n[c],n,s)),e&&(y(e)?s[e]=n[c]:k(e)&&(s[e.name?e.name:c]=e.value))}else if(-1!==qt.indexOf(c)){const o=n[c];v(o)&&(s[c]=t(e,o,r))}else i||d(s,c)||(s[c]=n[c]);return s}return v(n)&&(n=t(e,n,r)),n}function o(t,o,r,i=!1){return v(e.returnValue)&&(o=e.returnValue(t,o)),n(t,o,r,{},i)}return function(t,r){if(!d(e,t))return r;const i=e[t];return i?function(e,r){let s=i;v(i)&&(s=i(e));const c=[e=n(t,e,s.args,s.returnValue)];void 0!==r&&c.push(r);const a=wx[s.name||t].apply(wx,c);return Wt(t)?o(t,a,s.returnValue,zt(t)):a}:function(){}}}(t);return new Proxy({},{get:(t,r)=>d(t,r)?t[r]:d(e,r)?Ft(r,e[r]):d(fn,r)?Ft(r,fn[r]):Ft(r,o(r,n[r]))})}(kn,Object.freeze({__proto__:null,compressImage:Sn,getAppAuthorizeSetting:{returnValue:function(e,t){const{locationReducedAccuracy:n}=e;t.locationAccuracy="unsupported",!0===n?t.locationAccuracy="reduced":!1===n&&(t.locationAccuracy="full")}},getAppBaseInfo:ln,getDeviceInfo:un,getSystemInfo:rn,getSystemInfoSync:sn,getWindowInfo:pn,previewImage:cn,redirectTo:{},showActionSheet:an}),vn());let Pn;class jn{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Pn,!e&&Pn&&(this.index=(Pn.scopes||(Pn.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Pn;try{return Pn=this,e()}finally{Pn=t}}}on(){Pn=this}off(){Pn=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}const En=e=>{const t=new Set(e);return t.w=0,t.n=0,t},An=e=>(e.w&Rn)>0,In=e=>(e.n&Rn)>0,Tn=new WeakMap;let Mn=0,Rn=1;let Vn;const Ln=Symbol("iterate"),Dn=Symbol("Map key iterate");class Nn{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=Pn){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=Vn,t=Bn;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=Vn,Vn=this,Bn=!0,Rn=1<<++Mn,Mn<=30?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=Rn})(this):Hn(this),this.fn()}finally{Mn<=30&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const r=t[o];An(r)&&!In(r)?r.delete(e):t[n++]=r,r.w&=~Rn,r.n&=~Rn}t.length=n}})(this),Rn=1<<--Mn,Vn=this.parent,Bn=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){Vn===this?this.deferStop=!0:this.active&&(Hn(this),this.onStop&&this.onStop(),this.active=!1)}}function Hn(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Bn=!0;const Un=[];function zn(){Un.push(Bn),Bn=!1}function Wn(){const e=Un.pop();Bn=void 0===e||e}function Kn(e,t,n){if(Bn&&Vn){let o=Tn.get(e);o||Tn.set(e,o=new Map);let r=o.get(n);r||o.set(n,r=En());Fn(r,{effect:Vn,target:e,type:t,key:n})}}function Fn(e,t){let n=!1;Mn<=30?In(e)||(e.n|=Rn,n=!An(e)):n=!e.has(Vn),n&&(e.add(Vn),Vn.deps.push(e),Vn.onTrack&&Vn.onTrack(Object.assign({effect:Vn},t)))}function qn(e,t,n,o,r,i){const s=Tn.get(e);if(!s)return;let c=[];if("clear"===t)c=[...s.values()];else if("length"===n&&h(e)){const e=Number(o);s.forEach((t,n)=>{("length"===n||n>=e)&&c.push(t)})}else switch(void 0!==n&&c.push(s.get(n)),t){case"add":h(e)?S(n)&&c.push(s.get("length")):(c.push(s.get(Ln)),g(e)&&c.push(s.get(Dn)));break;case"delete":h(e)||(c.push(s.get(Ln)),g(e)&&c.push(s.get(Dn)));break;case"set":g(e)&&c.push(s.get(Ln))}const a={target:e,type:t,key:n,newValue:o,oldValue:r,oldTarget:i};if(1===c.length)c[0]&&Jn(c[0],a);else{const e=[];for(const t of c)t&&e.push(...t);Jn(En(e),a)}}function Jn(e,t){const n=h(e)?e:[...e];for(const o of n)o.computed&&Gn(o,t);for(const o of n)o.computed||Gn(o,t)}function Gn(e,t){(e!==Vn||e.allowRecurse)&&(e.onTrigger&&e.onTrigger(l({effect:e},t)),e.scheduler?e.scheduler():e.run())}const Yn=e("__proto__,__v_isRef,__isVue"),Zn=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(b)),Qn=io(),Xn=io(!1,!0),eo=io(!0),to=io(!0,!0),no=oo();function oo(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...e){const n=Fo(this);for(let t=0,r=this.length;t<r;t++)Kn(n,"get",t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Fo)):o}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...e){zn();const n=Fo(this)[t].apply(this,e);return Wn(),n}}),e}function ro(e){const t=Fo(this);return Kn(t,"has",e),t.hasOwnProperty(e)}function io(e=!1,t=!1){return function(n,o,r){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&r===(e?t?Do:Lo:t?Vo:Ro).get(n))return n;const i=h(n);if(!e){if(i&&d(no,o))return Reflect.get(no,o,r);if("hasOwnProperty"===o)return ro}const s=Reflect.get(n,o,r);return(b(o)?Zn.has(o):Yn(o))?s:(e||Kn(n,"get",o),t?s:Qo(s)?i&&S(o)?s:s.value:_(s)?e?Ho(s):No(s):s)}}function so(e=!1){return function(t,n,o,r){let i=t[n];if(Wo(i)&&Qo(i)&&!Qo(o))return!1;if(!e&&(Ko(o)||Wo(o)||(i=Fo(i),o=Fo(o)),!h(t)&&Qo(i)&&!Qo(o)))return i.value=o,!0;const s=h(t)&&S(n)?Number(n)<t.length:d(t,n),c=Reflect.set(t,n,o,r);return t===Fo(r)&&(s?V(o,i)&&qn(t,"set",n,o,i):qn(t,"add",n,o)),c}}const co={get:Qn,set:so(),deleteProperty:function(e,t){const n=d(e,t),o=e[t],r=Reflect.deleteProperty(e,t);return r&&n&&qn(e,"delete",t,void 0,o),r},has:function(e,t){const n=Reflect.has(e,t);return b(t)&&Zn.has(t)||Kn(e,"has",t),n},ownKeys:function(e){return Kn(e,"iterate",h(e)?"length":Ln),Reflect.ownKeys(e)}},ao={get:eo,set:(e,t)=>(String(t),!0),deleteProperty:(e,t)=>(String(t),!0)},uo=l({},co,{get:Xn,set:so(!0)}),lo=l({},ao,{get:to}),po=e=>e,fo=e=>Reflect.getPrototypeOf(e);function ho(e,t,n=!1,o=!1){const r=Fo(e=e.__v_raw),i=Fo(t);n||(t!==i&&Kn(r,"get",t),Kn(r,"get",i));const{has:s}=fo(r),c=o?po:n?Go:Jo;return s.call(r,t)?c(e.get(t)):s.call(r,i)?c(e.get(i)):void(e!==r&&e.get(t))}function go(e,t=!1){const n=this.__v_raw,o=Fo(n),r=Fo(e);return t||(e!==r&&Kn(o,"has",e),Kn(o,"has",r)),e===r?n.has(e):n.has(e)||n.has(r)}function mo(e,t=!1){return e=e.__v_raw,!t&&Kn(Fo(e),"iterate",Ln),Reflect.get(e,"size",e)}function vo(e){e=Fo(e);const t=Fo(this);return fo(t).has.call(t,e)||(t.add(e),qn(t,"add",e,e)),this}function yo(e,t){t=Fo(t);const n=Fo(this),{has:o,get:r}=fo(n);let i=o.call(n,e);i?Mo(n,o,e):(e=Fo(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?V(t,s)&&qn(n,"set",e,t,s):qn(n,"add",e,t),this}function bo(e){const t=Fo(this),{has:n,get:o}=fo(t);let r=n.call(t,e);r?Mo(t,n,e):(e=Fo(e),r=n.call(t,e));const i=o?o.call(t,e):void 0,s=t.delete(e);return r&&qn(t,"delete",e,void 0,i),s}function _o(){const e=Fo(this),t=0!==e.size,n=g(e)?new Map(e):new Set(e),o=e.clear();return t&&qn(e,"clear",void 0,void 0,n),o}function xo(e,t){return function(n,o){const r=this,i=r.__v_raw,s=Fo(i),c=t?po:e?Go:Jo;return!e&&Kn(s,"iterate",Ln),i.forEach((e,t)=>n.call(o,c(e),c(t),r))}}function wo(e,t,n){return function(...o){const r=this.__v_raw,i=Fo(r),s=g(i),c="entries"===e||e===Symbol.iterator&&s,a="keys"===e&&s,u=r[e](...o),l=n?po:t?Go:Jo;return!t&&Kn(i,"iterate",a?Dn:Ln),{next(){const{value:e,done:t}=u.next();return t?{value:e,done:t}:{value:c?[l(e[0]),l(e[1])]:l(e),done:t}},[Symbol.iterator](){return this}}}}function $o(e){return function(...t){t[0]&&t[0];return"delete"!==e&&this}}function Oo(){const e={get(e){return ho(this,e)},get size(){return mo(this)},has:go,add:vo,set:yo,delete:bo,clear:_o,forEach:xo(!1,!1)},t={get(e){return ho(this,e,!1,!0)},get size(){return mo(this)},has:go,add:vo,set:yo,delete:bo,clear:_o,forEach:xo(!1,!0)},n={get(e){return ho(this,e,!0)},get size(){return mo(this,!0)},has(e){return go.call(this,e,!0)},add:$o("add"),set:$o("set"),delete:$o("delete"),clear:$o("clear"),forEach:xo(!0,!1)},o={get(e){return ho(this,e,!0,!0)},get size(){return mo(this,!0)},has(e){return go.call(this,e,!0)},add:$o("add"),set:$o("set"),delete:$o("delete"),clear:$o("clear"),forEach:xo(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(r=>{e[r]=wo(r,!1,!1),n[r]=wo(r,!0,!1),t[r]=wo(r,!1,!0),o[r]=wo(r,!0,!0)}),[e,n,t,o]}const[ko,So,Co,Po]=Oo();function jo(e,t){const n=t?e?Po:Co:e?So:ko;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(d(n,o)&&o in t?n:t,o,r)}const Eo={get:jo(!1,!1)},Ao={get:jo(!1,!0)},Io={get:jo(!0,!1)},To={get:jo(!0,!0)};function Mo(e,t,n){const o=Fo(n);if(o!==n&&t.call(e,o)){O(e)}}const Ro=new WeakMap,Vo=new WeakMap,Lo=new WeakMap,Do=new WeakMap;function No(e){return Wo(e)?e:Uo(e,!1,co,Eo,Ro)}function Ho(e){return Uo(e,!0,ao,Io,Lo)}function Bo(e){return Uo(e,!0,lo,To,Do)}function Uo(e,t,n,o,r){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=(c=e).__v_skip||!Object.isExtensible(c)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(O(c));var c;if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function zo(e){return Wo(e)?zo(e.__v_raw):!(!e||!e.__v_isReactive)}function Wo(e){return!(!e||!e.__v_isReadonly)}function Ko(e){return!(!e||!e.__v_isShallow)}function Fo(e){const t=e&&e.__v_raw;return t?Fo(t):e}function qo(e){return((e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})})(e,"__v_skip",!0),e}const Jo=e=>_(e)?No(e):e,Go=e=>_(e)?Ho(e):e;function Yo(e){Bn&&Vn&&Fn((e=Fo(e)).dep||(e.dep=En()),{target:e,type:"get",key:"value"})}function Zo(e,t){const n=(e=Fo(e)).dep;n&&Jn(n,{target:e,type:"set",key:"value",newValue:t})}function Qo(e){return!(!e||!0!==e.__v_isRef)}function Xo(e){return function(e,t){if(Qo(e))return e;return new er(e,t)}(e,!1)}class er{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Fo(e),this._value=t?e:Jo(e)}get value(){return Yo(this),this._value}set value(e){const t=this.__v_isShallow||Ko(e)||Wo(e);e=t?e:Fo(e),V(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Jo(e),Zo(this,e))}}function tr(e){return Qo(e)?e.value:e}const nr={get:(e,t,n)=>tr(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return Qo(r)&&!Qo(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function or(e){return zo(e)?e:new Proxy(e,nr)}var rr;class ir{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[rr]=!1,this._dirty=!0,this.effect=new Nn(e,()=>{this._dirty||(this._dirty=!0,Zo(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Fo(this);return Yo(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}rr="__v_isReadonly";const sr=[];function cr(e){sr.push(e)}function ar(){sr.pop()}function ur(e,...t){zn();const n=sr.length?sr[sr.length-1].component:null,o=n&&n.appContext.config.warnHandler,r=function(){let e=sr[sr.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const o=e.component&&e.component.parent;e=o&&o.vnode}return t}();if(o)dr(o,n,11,[e+t.join(""),n&&n.proxy,r.map(({vnode:e})=>`at <${Cs(n,e.type)}>`).join("\n"),r]);else{const n=[`[Vue warn]: ${e}`,...t];r.length&&n.push("\n",...function(e){const t=[];return e.forEach((e,n)=>{t.push(...0===n?[]:["\n"],...function({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",o=!!e.component&&null==e.component.parent,r=` at <${Cs(e.component,e.type,o)}`,i=">"+n;return e.props?[r,...lr(e.props),i]:[r+i]}(e))}),t}(r))}Wn()}function lr(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(n=>{t.push(...pr(n,e[n]))}),n.length>3&&t.push(" ..."),t}function pr(e,t,n){return y(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):"number"==typeof t||"boolean"==typeof t||null==t?n?t:[`${e}=${t}`]:Qo(t)?(t=pr(e,Fo(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):v(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Fo(t),n?t:[`${e}=`,t])}const fr={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush. This is likely a Vue internals bug. Please open an issue at https://new-issue.vuejs.org/?repo=vuejs/core"};function dr(e,t,n,o){let r;try{r=o?e(...o):e()}catch(i){gr(i,t,n)}return r}function hr(e,t,n,o){if(v(e)){const r=dr(e,t,n,o);return r&&x(r)&&r.catch(e=>{gr(e,t,n)}),r}const r=[];for(let i=0;i<e.length;i++)r.push(hr(e[i],t,n,o));return r}function gr(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=fr[n]||n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void dr(s,null,10,[e,r,i])}!function(e,t,n){{const e=fr[t]||t;n&&cr(n),ur("Unhandled error"+(e?` during execution of ${e}`:"")),n&&ar()}}(0,n,r,o)}let mr=!1,vr=!1;const yr=[];let br=0;const _r=[];let xr=null,wr=0;const $r=Promise.resolve();let Or=null;function kr(e){const t=Or||$r;return e?t.then(this?e.bind(this):e):t}function Sr(e){yr.length&&yr.includes(e,mr&&e.allowRecurse?br+1:br)||(null==e.id?yr.push(e):yr.splice(function(e){let t=br+1,n=yr.length;for(;t<n;){const o=t+n>>>1;Er(yr[o])<e?t=o+1:n=o}return t}(e.id),0,e),Cr())}function Cr(){mr||vr||(vr=!0,Or=$r.then(Ir))}function Pr(e){h(e)?_r.push(...e):xr&&xr.includes(e,e.allowRecurse?wr+1:wr)||_r.push(e),Cr()}function jr(e,t=(mr?br+1:0)){for(e=e||new Map;t<yr.length;t++){const n=yr[t];if(n&&n.pre){if(Tr(e,n))continue;yr.splice(t,1),t--,n()}}}const Er=e=>null==e.id?1/0:e.id,Ar=(e,t)=>{const n=Er(e)-Er(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Ir(e){vr=!1,mr=!0,e=e||new Map,yr.sort(Ar);const t=t=>Tr(e,t);try{for(br=0;br<yr.length;br++){const e=yr[br];if(e&&!1!==e.active){if(t(e))continue;dr(e,null,14)}}}finally{br=0,yr.length=0,function(e){if(_r.length){const t=[...new Set(_r)];if(_r.length=0,xr)return void xr.push(...t);for(xr=t,e=e||new Map,xr.sort((e,t)=>Er(e)-Er(t)),wr=0;wr<xr.length;wr++)Tr(e,xr[wr])||xr[wr]();xr=null,wr=0}}(e),mr=!1,Or=null,(yr.length||_r.length)&&Ir(e)}}function Tr(e,t){if(e.has(t)){const n=e.get(t);if(n>100){const e=t.ownerInstance,n=e&&Ss(e.type);return ur(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`),!0}e.set(t,n+1)}else e.set(t,1)}let Mr,Rr=[],Vr=!1;function Lr(e,...t){Mr?Mr.emit(e,...t):Vr||Rr.push({event:e,args:t})}function Dr(e,t){var n,o;if(Mr=e,Mr)Mr.enabled=!0,Rr.forEach(({event:e,args:t})=>Mr.emit(e,...t)),Rr=[];else if("undefined"!=typeof window&&window.HTMLElement&&!(null===(o=null===(n=window.navigator)||void 0===n?void 0:n.userAgent)||void 0===o?void 0:o.includes("jsdom"))){(t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(e=>{Dr(e,t)}),setTimeout(()=>{Mr||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,Vr=!0,Rr=[])},3e3)}else Vr=!0,Rr=[]}const Nr=Ur("component:added"),Hr=Ur("component:updated"),Br=Ur("component:removed");function Ur(e){return t=>{Lr(e,t.appContext.app,t.uid,0===t.uid?void 0:t.parent?t.parent.uid:0,t)}}const zr=Kr("perf:start"),Wr=Kr("perf:end");function Kr(e){return(t,n,o)=>{Lr(e,t.appContext.app,t.uid,t,n,o)}}function Fr(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||o;{const{emitsOptions:o,propsOptions:[r]}=e;if(o)if(t in o){const e=o[t];if(v(e)){e(...n)||ur(`Invalid event arguments: event validation failed for event "${t}".`)}}else r&&R(t)in r||ur(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${R(t)}" prop.`)}let i=n;const s=t.startsWith("update:"),c=s&&t.slice(7);if(c&&c in r){const e=`${"modelValue"===c?"model":c}Modifiers`,{number:t,trim:s}=r[e]||o;s&&(i=n.map(e=>y(e)?e.trim():e)),t&&(i=n.map(D))}!function(e,t,n){Lr("component:emit",e.appContext.app,e,t,n)}(e,t,i);{const n=t.toLowerCase();n!==t&&r[R(n)]&&ur(`Event "${n}" is emitted in component ${Cs(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${T(t)}" instead of "${t}".`)}let a,u=r[a=R(t)]||r[a=R(A(t))];!u&&s&&(u=r[a=R(T(t))]),u&&hr(u,e,6,i);const l=r[a+"Once"];if(l){if(e.emitted){if(e.emitted[a])return}else e.emitted={};e.emitted[a]=!0,hr(l,e,6,i)}}function qr(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},c=!1;if(!v(e)){const o=e=>{const n=qr(e,t,!0);n&&(c=!0,l(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||c?(h(i)?i.forEach(e=>s[e]=null):l(s,i),_(e)&&o.set(e,s),s):(_(e)&&o.set(e,null),null)}function Jr(e,t){return!(!e||!a(t))&&(t=t.slice(2).replace(/Once$/,""),d(e,t[0].toLowerCase()+t.slice(1))||d(e,T(t))||d(e,t))}let Gr=null;function Yr(e){const t=Gr;return Gr=e,e&&e.type.__scopeId,t}function Zr(e,t,n=!1){const o=ds||Gr;if(o){const r=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&v(t)?t.call(o.proxy):t;ur(`injection "${String(e)}" not found.`)}else ur("inject() can only be used inside setup() or functional components.")}const Qr={};function Xr(e,t,n){return v(t)||ur("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),ei(e,t,n)}function ei(e,t,{immediate:n,deep:r,flush:s,onTrack:c,onTrigger:a}=o){t||(void 0!==n&&ur('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),void 0!==r&&ur('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'));const u=e=>{ur("Invalid watch source: ",e,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},l=Pn===(null==ds?void 0:ds.scope)?ds:null;let f,d,g=!1,m=!1;if(Qo(e)?(f=()=>e.value,g=Ko(e)):zo(e)?(f=()=>e,r=!0):h(e)?(m=!0,g=e.some(e=>zo(e)||Ko(e)),f=()=>e.map(e=>Qo(e)?e.value:zo(e)?oi(e):v(e)?dr(e,l,2):void u(e))):v(e)?f=t?()=>dr(e,l,2):()=>{if(!l||!l.isUnmounted)return d&&d(),hr(e,l,3,[y])}:(f=i,u(e)),t&&r){const e=f;f=()=>oi(e())}let y=e=>{d=w.onStop=()=>{dr(e,l,4)}},b=m?new Array(e.length).fill(Qr):Qr;const _=()=>{if(w.active)if(t){const e=w.run();(r||g||(m?e.some((e,t)=>V(e,b[t])):V(e,b)))&&(d&&d(),hr(t,l,3,[e,b===Qr?void 0:m&&b[0]===Qr?[]:b,y]),b=e)}else w.run()};let x;_.allowRecurse=!!t,"sync"===s?x=_:"post"===s?x=()=>is(_,l&&l.suspense):(_.pre=!0,l&&(_.id=l.uid),x=()=>Sr(_));const w=new Nn(f,x);w.onTrack=c,w.onTrigger=a,t?n?_():b=w.run():"post"===s?is(w.run.bind(w),l&&l.suspense):w.run();return()=>{w.stop(),l&&l.scope&&p(l.scope.effects,w)}}function ti(e,t,n){const o=this.proxy,r=y(e)?e.includes(".")?ni(o,e):()=>o[e]:e.bind(o,o);let i;v(t)?i=t:(i=t.handler,n=t);const s=ds;hs(this);const c=ei(r,i.bind(o),n);return s?hs(s):gs(),c}function ni(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function oi(e,t){if(!_(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),Qo(e))oi(e.value,t);else if(h(e))for(let n=0;n<e.length;n++)oi(e[n],t);else if(m(e)||g(e))e.forEach(e=>{oi(e,t)});else if(k(e))for(const n in e)oi(e[n],t);return e}const ri=e=>e.type.__isKeepAlive;function ii(e,t){ci(e,"a",t)}function si(e,t){ci(e,"da",t)}function ci(e,t,n=ds){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(ui(t,o,n),n){let e=n.parent;for(;e&&e.parent;)ri(e.parent.vnode)&&ai(o,t,n,e),e=e.parent}}function ai(e,t,n,o){const r=ui(t,e,o,!0);mi(()=>{p(o[t],r)},n)}function ui(e,t,n=ds,o=!1){if(n){(function(e){return ve.indexOf(e)>-1})(e)&&(n=n.root);const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;zn(),hs(n);const r=hr(t,n,e,o);return gs(),Wn(),r});return o?r.unshift(i):r.push(i),i}ur(`${R((fr[e]||e.replace(/^on/,"")).replace(/ hook$/,""))} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup().`)}const li=e=>(t,n=ds)=>(!bs||"sp"===e)&&ui(e,(...e)=>t(...e),n),pi=li("bm"),fi=li("m"),di=li("bu"),hi=li("u"),gi=li("bum"),mi=li("um"),vi=li("sp"),yi=li("rtg"),bi=li("rtc");function _i(e,t=ds){ui("ec",e,t)}function xi(e){P(e)&&ur("Do not use built-in directive ids as custom directive id: "+e)}const wi=e=>e?ys(e)?$s(e)||e.proxy:wi(e.parent):null,$i=l(Object.create(null),{$:e=>e,$el:e=>e.__$el||(e.__$el={}),$data:e=>e.data,$props:e=>Bo(e.props),$attrs:e=>Bo(e.attrs),$slots:e=>Bo(e.slots),$refs:e=>Bo(e.refs),$parent:e=>wi(e.parent),$root:e=>wi(e.root),$emit:e=>e.emit,$options:e=>Ai(e),$forceUpdate:e=>e.f||(e.f=()=>Sr(e.update)),$watch:e=>ti.bind(e)}),Oi=e=>"_"===e||"$"===e,ki=(e,t)=>e!==o&&!e.__isScriptSetup&&d(e,t),Si={get({_:e},t){const{ctx:n,setupState:r,data:i,props:s,accessCache:c,type:a,appContext:u}=e;if("__isVue"===t)return!0;let l;if("$"!==t[0]){const a=c[t];if(void 0!==a)switch(a){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return s[t]}else{if(ki(r,t))return c[t]=1,r[t];if(i!==o&&d(i,t))return c[t]=2,i[t];if((l=e.propsOptions[0])&&d(l,t))return c[t]=3,s[t];if(n!==o&&d(n,t))return c[t]=4,n[t];Ci&&(c[t]=0)}}const p=$i[t];let f,h;return p?("$attrs"===t&&Kn(e,"get",t),p(e)):(f=a.__cssModules)&&(f=f[t])?f:n!==o&&d(n,t)?(c[t]=4,n[t]):(h=u.config.globalProperties,d(h,t)?h[t]:void(!Gr||y(t)&&0===t.indexOf("__v")||(i!==o&&Oi(t[0])&&d(i,t)?ur(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===Gr&&ur(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))))},set({_:e},t,n){const{data:r,setupState:i,ctx:s}=e;return ki(i,t)?(i[t]=n,!0):i.__isScriptSetup&&d(i,t)?(ur(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):r!==o&&d(r,t)?(r[t]=n,!0):d(e.props,t)?(ur(`Attempting to mutate prop "${t}". Props are readonly.`),!1):"$"===t[0]&&t.slice(1)in e?(ur(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):(t in e.appContext.config.globalProperties?Object.defineProperty(s,t,{enumerable:!0,configurable:!0,value:n}):s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:s}},c){let a;return!!n[c]||e!==o&&d(e,c)||ki(t,c)||(a=s[0])&&d(a,c)||d(r,c)||d($i,c)||d(i.config.globalProperties,c)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:d(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};Si.ownKeys=e=>(ur("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e));let Ci=!0;function Pi(e){const t=Ai(e),n=e.proxy,o=e.ctx;Ci=!1,t.beforeCreate&&ji(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:c,watch:a,provide:u,inject:l,created:p,beforeMount:f,mounted:d,beforeUpdate:g,updated:m,activated:y,deactivated:b,beforeDestroy:w,beforeUnmount:$,destroyed:O,unmounted:k,render:S,renderTracked:C,renderTriggered:P,errorCaptured:j,serverPrefetch:E,expose:A,inheritAttrs:I,components:T,directives:M,filters:R}=t,V=function(){const e=Object.create(null);return(t,n)=>{e[n]?ur(`${t} property "${n}" is already defined in ${e[n]}.`):e[n]=t}}();{const[t]=e.propsOptions;if(t)for(const e in t)V("Props",e)}if(l&&function(e,t,n=i,o=!1){h(e)&&(e=Ri(e));for(const r in e){const i=e[r];let s;s=_(i)?"default"in i?Zr(i.from||r,i.default,!0):Zr(i.from||r):Zr(i),Qo(s)?o?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e}):(ur(`injected property "${r}" is a ref and will be auto-unwrapped and no longer needs \`.value\` in the next minor release. To opt-in to the new behavior now, set \`app.config.unwrapInjectedRef = true\` (this config is temporary and will not be needed in the future.)`),t[r]=s):t[r]=s,n("Inject",r)}}(l,o,V,e.appContext.config.unwrapInjectedRef),c)for(const i in c){const e=c[i];v(e)?(Object.defineProperty(o,i,{value:e.bind(n),configurable:!0,enumerable:!0,writable:!0}),V("Methods",i)):ur(`Method "${i}" has type "${typeof e}" in the component definition. Did you reference the function correctly?`)}if(r){v(r)||ur("The data option must be a function. Plain object usage is no longer supported.");const t=r.call(n,n);if(x(t)&&ur("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),_(t)){e.data=No(t);for(const e in t)V("Data",e),Oi(e[0])||Object.defineProperty(o,e,{configurable:!0,enumerable:!0,get:()=>t[e],set:i})}else ur("data() should return an object.")}if(Ci=!0,s)for(const h in s){const e=s[h],t=v(e)?e.bind(n,n):v(e.get)?e.get.bind(n,n):i;t===i&&ur(`Computed property "${h}" has no getter.`);const r=!v(e)&&v(e.set)?e.set.bind(n):()=>{ur(`Write operation failed: computed property "${h}" is readonly.`)},c=Ps({get:t,set:r});Object.defineProperty(o,h,{enumerable:!0,configurable:!0,get:()=>c.value,set:e=>c.value=e}),V("Computed",h)}if(a)for(const i in a)Ei(a[i],o,n,i);if(u){const e=v(u)?u.call(n):u;Reflect.ownKeys(e).forEach(t=>{!function(e,t){if(ds){let n=ds.provides;const o=ds.parent&&ds.parent.provides;o===n&&(n=ds.provides=Object.create(o)),n[e]=t,"app"===ds.type.mpType&&ds.appContext.app.provide(e,t)}else ur("provide() can only be used inside setup().")}(t,e[t])})}function L(e,t){h(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(p&&ji(p,e,"c"),L(pi,f),L(fi,d),L(di,g),L(hi,m),L(ii,y),L(si,b),L(_i,j),L(bi,C),L(yi,P),L(gi,$),L(mi,k),L(vi,E),h(A))if(A.length){const t=e.exposed||(e.exposed={});A.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})})}else e.exposed||(e.exposed={});S&&e.render===i&&(e.render=S),null!=I&&(e.inheritAttrs=I),T&&(e.components=T),M&&(e.directives=M),e.ctx.$onApplyOptions&&e.ctx.$onApplyOptions(t,e,n)}function ji(e,t,n){hr(h(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ei(e,t,n,o){const r=o.includes(".")?ni(n,o):()=>n[o];if(y(e)){const n=t[e];v(n)?Xr(r,n):ur(`Invalid watch handler specified by key "${e}"`,n)}else if(v(e))Xr(r,e.bind(n));else if(_(e))if(h(e))e.forEach(e=>Ei(e,t,n,o));else{const o=v(e.handler)?e.handler.bind(n):t[e.handler];v(o)?Xr(r,o,e):ur(`Invalid watch handler specified by key "${e.handler}"`,o)}else ur(`Invalid watch option: "${o}"`,e)}function Ai(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,c=i.get(t);let a;return c?a=c:r.length||n||o?(a={},r.length&&r.forEach(e=>Ii(a,e,s,!0)),Ii(a,t,s)):a=t,_(t)&&i.set(t,a),a}function Ii(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&Ii(e,i,n,!0),r&&r.forEach(t=>Ii(e,t,n,!0));for(const s in t)if(o&&"expose"===s)ur('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const o=Ti[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const Ti={data:Mi,props:Li,emits:Li,methods:Li,computed:Li,beforeCreate:Vi,created:Vi,beforeMount:Vi,mounted:Vi,beforeUpdate:Vi,updated:Vi,beforeDestroy:Vi,beforeUnmount:Vi,destroyed:Vi,unmounted:Vi,activated:Vi,deactivated:Vi,errorCaptured:Vi,serverPrefetch:Vi,components:Li,directives:Li,watch:function(e,t){if(!e)return t;if(!t)return e;const n=l(Object.create(null),e);for(const o in t)n[o]=Vi(e[o],t[o]);return n},provide:Mi,inject:function(e,t){return Li(Ri(e),Ri(t))}};function Mi(e,t){return t?e?function(){return l(v(e)?e.call(this,this):e,v(t)?t.call(this,this):t)}:t:e}function Ri(e){if(h(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Vi(e,t){return e?[...new Set([].concat(e,t))]:t}function Li(e,t){return e?l(l(Object.create(null),e),t):t}function Di(e,t,n,o=!1){const r={},i={};e.propsDefaults=Object.create(null),Ni(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);Fi(t||{},r,e),n?e.props=o?r:Uo(r,!1,uo,Ao,Vo):e.type.props?e.props=r:e.props=i,e.attrs=i}function Ni(e,t,n,r){const[i,s]=e.propsOptions;let c,a=!1;if(t)for(let o in t){if(C(o))continue;const u=t[o];let l;i&&d(i,l=A(o))?s&&s.includes(l)?(c||(c={}))[l]=u:n[l]=u:Jr(e.emitsOptions,o)||o in r&&u===r[o]||(r[o]=u,a=!0)}if(s){const t=Fo(n),r=c||o;for(let o=0;o<s.length;o++){const c=s[o];n[c]=Hi(i,t,c,r[c],e,!d(r,c))}}return a}function Hi(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=d(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&v(e)){const{propsDefaults:i}=r;n in i?o=i[n]:(hs(r),o=i[n]=e.call(null,t),gs())}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==T(n)||(o=!0))}return o}function Bi(e,t,n=!1){const i=t.propsCache,s=i.get(e);if(s)return s;const c=e.props,a={},u=[];let p=!1;if(!v(e)){const o=e=>{p=!0;const[n,o]=Bi(e,t,!0);l(a,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!c&&!p)return _(e)&&i.set(e,r),r;if(h(c))for(let r=0;r<c.length;r++){y(c[r])||ur("props must be strings when using array syntax.",c[r]);const e=A(c[r]);Ui(e)&&(a[e]=o)}else if(c){_(c)||ur("invalid props options",c);for(const e in c){const t=A(e);if(Ui(t)){const n=c[e],o=a[t]=h(n)||v(n)?{type:n}:Object.assign({},n);if(o){const e=Ki(Boolean,o.type),n=Ki(String,o.type);o[0]=e>-1,o[1]=n<0||e<n,(e>-1||d(o,"default"))&&u.push(t)}}}}const f=[a,u];return _(e)&&i.set(e,f),f}function Ui(e){return"$"!==e[0]||(ur(`Invalid prop name: "${e}" is a reserved property.`),!1)}function zi(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function Wi(e,t){return zi(e)===zi(t)}function Ki(e,t){return h(t)?t.findIndex(t=>Wi(t,e)):v(t)&&Wi(t,e)?0:-1}function Fi(e,t,n){const o=Fo(t),r=n.propsOptions[0];for(const i in r){let t=r[i];null!=t&&qi(i,o[i],t,!d(e,i)&&!d(e,T(i)))}}function qi(e,t,n,o){const{type:r,required:i,validator:s}=n;if(i&&o)ur('Missing required prop: "'+e+'"');else if(null!=t||n.required){if(null!=r&&!0!==r){let n=!1;const o=h(r)?r:[r],i=[];for(let e=0;e<o.length&&!n;e++){const{valid:r,expectedType:s}=Gi(t,o[e]);i.push(s||""),n=r}if(!n)return void ur(function(e,t,n){let o=`Invalid prop: type check failed for prop "${e}". Expected ${n.map(M).join(" | ")}`;const r=n[0],i=O(t),s=Yi(t,r),c=Yi(t,i);1===n.length&&Zi(r)&&!function(...e){return e.some(e=>"boolean"===e.toLowerCase())}(r,i)&&(o+=` with value ${s}`);o+=`, got ${i} `,Zi(i)&&(o+=`with value ${c}.`);return o}(e,t,i))}s&&!s(t)&&ur('Invalid prop: custom validator check failed for prop "'+e+'".')}}const Ji=e("String,Number,Boolean,Function,Symbol,BigInt");function Gi(e,t){let n;const o=zi(t);if(Ji(o)){const r=typeof e;n=r===o.toLowerCase(),n||"object"!==r||(n=e instanceof t)}else n="Object"===o?_(e):"Array"===o?h(e):"null"===o?null===e:e instanceof t;return{valid:n,expectedType:o}}function Yi(e,t){return"String"===t?`"${e}"`:"Number"===t?`${Number(e)}`:`${e}`}function Zi(e){return["string","number","boolean"].some(t=>e.toLowerCase()===t)}function Qi(){return{app:null,config:{isNativeTag:s,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Xi,es,ts=0;function ns(e,t){e.appContext.config.performance&&rs()&&es.mark(`vue-${t}-${e.uid}`),zr(e,t,rs()?es.now():Date.now())}function os(e,t){if(e.appContext.config.performance&&rs()){const n=`vue-${t}-${e.uid}`,o=n+":end";es.mark(o),es.measure(`<${Cs(e,e.type)}> ${t}`,n,o),es.clearMarks(n),es.clearMarks(o)}Wr(e,t,rs()?es.now():Date.now())}function rs(){return void 0!==Xi||("undefined"!=typeof window&&window.performance?(Xi=!0,es=window.performance):Xi=!1),Xi}const is=Pr,ss=Symbol("Fragment"),cs=Symbol("Text"),as=Symbol("Comment"),us=Symbol("Static");const ls=Qi();let ps=0;function fs(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||ls,c={uid:ps++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new jn(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Bi(r,s),emitsOptions:qr(r,s),emit:null,emitted:null,propsDefaults:o,inheritAttrs:r.inheritAttrs,ctx:o,data:o,props:o,attrs:o,slots:o,refs:o,setupState:o,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return c.ctx=function(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys($i).forEach(n=>{Object.defineProperty(t,n,{configurable:!0,enumerable:!1,get:()=>$i[n](e),set:i})}),t}(c),c.root=t?t.root:c,c.emit=Fr.bind(null,c),e.ce&&e.ce(c),c}let ds=null;const hs=e=>{ds=e,e.scope.on()},gs=()=>{ds&&ds.scope.off(),ds=null},ms=e("slot,component");function vs(e,t){const n=t.isNativeTag||s;(ms(e)||n(e))&&ur("Do not use built-in or reserved HTML elements as component id: "+e)}function ys(e){return 4&e.vnode.shapeFlag}let bs=!1;function _s(e,t=!1){bs=t;const{props:n}=e.vnode,o=ys(e);Di(e,n,o,t);const r=o?function(e,t){const n=e.type;n.name&&vs(n.name,e.appContext.config);if(n.components){const t=Object.keys(n.components);for(let n=0;n<t.length;n++)vs(t[n],e.appContext.config)}if(n.directives){const e=Object.keys(n.directives);for(let t=0;t<e.length;t++)xi(e[t])}n.compilerOptions&&xs()&&ur('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.');e.accessCache=Object.create(null),e.proxy=qo(new Proxy(e.ctx,Si)),function(e){const{ctx:t,propsOptions:[n]}=e;n&&Object.keys(n).forEach(n=>{Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>e.props[n],set:i})})}(e);const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{if(e.exposed&&ur("expose() should be called only once per setup()."),null!=t){let e=typeof t;"object"===e&&(h(t)?e="array":Qo(t)&&(e="ref")),"object"!==e&&ur(`expose() should be passed a plain object, received ${e}.`)}e.exposed=t||{}};let n;return Object.freeze({get attrs(){return n||(n=function(e){return new Proxy(e.attrs,{get:(t,n)=>(Kn(e,"get","$attrs"),t[n]),set:()=>(ur("setupContext.attrs is readonly."),!1),deleteProperty:()=>(ur("setupContext.attrs is readonly."),!1)})}(e))},get slots(){return Bo(e.slots)},get emit(){return(t,...n)=>e.emit(t,...n)},expose:t})}(e):null;hs(e),zn();const r=dr(o,e,0,[Bo(e.props),n]);Wn(),gs(),x(r)?(r.then(gs,gs),ur("setup() returned a Promise, but the version of Vue you are using does not support it yet.")):function(e,t,n){v(t)?e.render=t:_(t)?((o=t)&&!0===o.__v_isVNode&&ur("setup() should not return VNodes directly - return a render function instead."),e.devtoolsRawSetupState=t,e.setupState=or(t),function(e){const{ctx:t,setupState:n}=e;Object.keys(Fo(n)).forEach(e=>{if(!n.__isScriptSetup){if(Oi(e[0]))return void ur(`setup() return property ${JSON.stringify(e)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);Object.defineProperty(t,e,{enumerable:!0,configurable:!0,get:()=>n[e],set:i})}})}(e)):void 0!==t&&ur("setup() should return an object. Received: "+(null===t?"null":typeof t));var o;ws(e,n)}(e,r,t)}else ws(e,t)}(e,t):void 0;return bs=!1,r}const xs=()=>!0;function ws(e,t,n){const o=e.type;e.render||(e.render=o.render||i),hs(e),zn(),Pi(e),Wn(),gs(),o.render||e.render!==i||t||(o.template?ur('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):ur("Component is missing template or render function."))}function $s(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(or(qo(e.exposed)),{get:(t,n)=>n in t?t[n]:e.proxy[n],has:(e,t)=>t in e||t in $i}))}const Os=/(?:^|[-_])(\w)/g,ks=e=>e.replace(Os,e=>e.toUpperCase()).replace(/[-_]/g,"");function Ss(e,t=!0){return v(e)?e.displayName||e.name:e.name||t&&e.__name}function Cs(e,t,n=!1){let o=Ss(t);if(!o&&t.__file){const e=t.__file.match(/([^/\\]+)\.\w+$/);e&&(o=e[1])}if(!o&&e&&e.parent){const n=e=>{for(const n in e)if(e[n]===t)return n};o=n(e.components||e.parent.type.components)||n(e.appContext.components)}return o?ks(o):n?"App":"Anonymous"}const Ps=(e,t)=>function(e,t,n=!1){let o,r;const i=v(e);i?(o=e,r=()=>{}):(o=e.get,r=e.set);const s=new ir(o,r,i||!r,n);return t&&!n&&(s.effect.onTrack=t.onTrack,s.effect.onTrigger=t.onTrigger),s}(e,t,bs),js="3.2.47";function Es(e){return tr(e)}const As="[object Array]",Is="[object Object]";function Ts(e,t){const n={};return Ms(e,t),Rs(e,t,"",n),n}function Ms(e,t){if((e=Es(e))===t)return;const n=$(e),o=$(t);if(n==Is&&o==Is)for(let r in t){const n=e[r];void 0===n?e[r]=null:Ms(n,t[r])}else n==As&&o==As&&e.length>=t.length&&t.forEach((t,n)=>{Ms(e[n],t)})}function Rs(e,t,n,o){if((e=Es(e))===t)return;const r=$(e),i=$(t);if(r==Is)if(i!=Is||Object.keys(e).length<Object.keys(t).length)Vs(o,n,e);else for(let s in e){const r=Es(e[s]),i=t[s],c=$(r),a=$(i);if(c!=As&&c!=Is)r!=i&&Vs(o,(""==n?"":n+".")+s,r);else if(c==As)a!=As||r.length<i.length?Vs(o,(""==n?"":n+".")+s,r):r.forEach((e,t)=>{Rs(e,i[t],(""==n?"":n+".")+s+"["+t+"]",o)});else if(c==Is)if(a!=Is||Object.keys(r).length<Object.keys(i).length)Vs(o,(""==n?"":n+".")+s,r);else for(let e in r)Rs(r[e],i[e],(""==n?"":n+".")+s+"."+e,o)}else r==As?i!=As||e.length<t.length?Vs(o,n,e):e.forEach((e,r)=>{Rs(e,t[r],n+"["+r+"]",o)}):Vs(o,n,e)}function Vs(e,t,n){e[t]=n}function Ls(e){const t=e.ctx.__next_tick_callbacks;if(t&&t.length){const e=t.slice(0);t.length=0;for(let t=0;t<e.length;t++)e[t]()}}function Ds(e,t){const n=e.ctx;if(!n.__next_tick_pending&&!function(e){return yr.includes(e.update)}(e))return kr(t&&t.bind(e.proxy));let o;return n.__next_tick_callbacks||(n.__next_tick_callbacks=[]),n.__next_tick_callbacks.push(()=>{t?dr(t.bind(e.proxy),e,14):o&&o(e.proxy)}),new Promise(e=>{o=e})}function Ns(e,t){const n=typeof(e=Es(e));if("object"===n&&null!==e){let n=t.get(e);if(void 0!==n)return n;if(h(e)){const o=e.length;n=new Array(o),t.set(e,n);for(let r=0;r<o;r++)n[r]=Ns(e[r],t)}else{n={},t.set(e,n);for(const o in e)d(e,o)&&(n[o]=Ns(e[o],t))}return n}if("symbol"!==n)return e}function Hs(e){return Ns(e,"undefined"!=typeof WeakMap?new WeakMap:new Map)}function Bs(e,t,n){if(!t)return;t=Hs(t);const o=e.ctx,r=o.mpType;if("page"===r||"component"===r){t.r0=1;const r=o.$scope,i=Object.keys(t),s=Ts(t,n||function(e,t){const n=e.data,o=Object.create(null);return t.forEach(e=>{o[e]=n[e]}),o}(r,i));Object.keys(s).length?(o.__next_tick_pending=!0,r.setData(s,()=>{o.__next_tick_pending=!1,Ls(e)}),jr()):Ls(e)}}function Us(e,t,n){t.appContext.config.globalProperties.$applyOptions(e,t,n);const o=e.computed;if(o){const e=Object.keys(o);if(e.length){const n=t.ctx;n.$computedKeys||(n.$computedKeys=[]),n.$computedKeys.push(...e)}}delete t.ctx.$onApplyOptions}function zs(e,t=!1){const{setupState:n,$templateRefs:o,ctx:{$scope:r,$mpPlatform:i}}=e;if("mp-alipay"===i)return;if(!o||!r)return;if(t)return o.forEach(e=>Ws(e,null,n));const s="mp-baidu"===i||"mp-toutiao"===i,c=e=>{const t=(r.selectAllComponents(".r")||[]).concat(r.selectAllComponents(".r-i-f")||[]);return e.filter(e=>{const o=function(e,t){const n=e.find(e=>e&&(e.properties||e.props).uI===t);if(n){const e=n.$vm;return e?$s(e.$)||e:function(e){_(e)&&qo(e);return e}(n)}return null}(t,e.i);return!(!s||null!==o)||(Ws(e,o,n),!1)})},a=()=>{const t=c(o);t.length&&e.proxy&&e.proxy.$scope&&e.proxy.$scope.setData({r1:1},()=>{c(t)})};r._$setRef?r._$setRef(a):Ds(e,a)}function Ws({r:e,f:t},n,o){if(v(e))e(n,{});else{const r=y(e),i=Qo(e);if(r||i)if(t){if(!i)return;h(e.value)||(e.value=[]);const t=e.value;if(-1===t.indexOf(n)){if(t.push(n),!n)return;gi(()=>p(t,n),n.$)}}else r?d(o,e)&&(o[e]=n):Qo(e)?e.value=n:Ks(e);else Ks(e)}}function Ks(e){ur("Invalid template ref type:",e,`(${typeof e})`)}var Fs,qs;(qs=Fs||(Fs={})).APP="app",qs.PAGE="page",qs.COMPONENT="component";const Js=Pr;function Gs(e,t){const n=e.component=fs(e,t.parentComponent,null);return n.ctx.$onApplyOptions=Us,n.ctx.$children=[],"app"===t.mpType&&(n.render=i),t.onBeforeSetup&&t.onBeforeSetup(n,t),cr(e),ns(n,"mount"),ns(n,"init"),_s(n),os(n,"init"),t.parentComponent&&n.proxy&&t.parentComponent.ctx.$children.push($s(n)||n.proxy),function(e){const t=ec.bind(e);e.$updateScopedSlots=()=>kr(()=>Sr(t));const n=()=>{if(e.isMounted){const{next:t,bu:n,u:o}=e;cr(t||e.vnode),tc(e,!1),Xs(),n&&L(n),tc(e,!0),ns(e,"patch"),Bs(e,Zs(e)),os(e,"patch"),o&&Js(o),Hr(e),ar()}else gi(()=>{zs(e,!0)},e),ns(e,"patch"),Bs(e,Zs(e)),os(e,"patch"),Nr(e)},o=e.effect=new Nn(n,()=>Sr(e.update),e.scope),r=e.update=o.run.bind(o);r.id=e.uid,tc(e,!0),o.onTrack=e.rtc?t=>L(e.rtc,t):void 0,o.onTrigger=e.rtg?t=>L(e.rtg,t):void 0,r.ownerInstance=e,r()}(n),ar(),os(n,"mount"),n.proxy}const Ys=e=>{let t;for(const n in e)("class"===n||"style"===n||a(n))&&((t||(t={}))[n]=e[n]);return t};function Zs(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:c,attrs:a,emit:u,render:l,renderCache:p,data:f,setupState:d,ctx:h,uid:g,appContext:{app:{config:{globalProperties:{pruneComponentPropsCache:m}}}},inheritAttrs:v}=e;let y;e.$templateRefs=[],e.$ei=0,m(g),e.__counter=0===e.__counter?1:0;const b=Yr(e);try{if(4&n.shapeFlag){Qs(v,i,s,a);const e=r||o;y=l.call(e,e,p,i,d,f,h)}else{Qs(v,i,s,t.props?a:Ys(a));const e=t;y=e.length>1?e(i,{attrs:a,slots:c,emit:u}):e(i,null)}}catch(_){gr(_,e,1),y=!1}return zs(e),Yr(b),y}function Qs(e,t,n,o){if(t&&o&&!1!==e){const e=Object.keys(o).filter(e=>"class"!==e&&"style"!==e);if(!e.length)return;n&&e.some(u)?e.forEach(e=>{u(e)&&e.slice(9)in n||(t[e]=o[e])}):e.forEach(e=>t[e]=o[e])}}const Xs=e=>{zn(),jr(),Wn()};function ec(){const e=this.$scopedSlotsData;if(!e||0===e.length)return;const t=this.ctx.$scope,n=t.data,o=Object.create(null);e.forEach(({path:e,index:t,data:r})=>{const i=de(n,e),s=y(t)?`${e}.${t}`:`${e}[${t}]`;if(void 0===i||void 0===i[t])o[s]=r;else{const e=Ts(r,i[t]);Object.keys(e).forEach(t=>{o[s+"."+t]=e[t]})}}),e.length=0,Object.keys(o).length&&t.setData(o)}function tc({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function nc(e){const{bum:t,scope:n,update:o,um:r}=e;var i;t&&L(t),n.stop(),o&&(o.active=!1),r&&Js(r),Js(()=>{e.isUnmounted=!0}),i=e,Mr&&"function"==typeof Mr.cleanupBuffer&&!Mr.cleanupBuffer(i)&&Br(i)}const oc=function(e,t=null){v(e)||(e=Object.assign({},e)),null==t||_(t)||(ur("root props passed to app.mount() must be an object."),t=null);const n=Qi(),o=new Set,r=n.app={_uid:ts++,_component:e,_props:t,_container:null,_context:n,_instance:null,version:js,get config(){return n.config},set config(e){ur("app.config cannot be replaced. Modify individual options instead.")},use:(e,...t)=>(o.has(e)?ur("Plugin has already been applied to target app."):e&&v(e.install)?(o.add(e),e.install(r,...t)):v(e)?(o.add(e),e(r,...t)):ur('A plugin must either be a function or an object with an "install" function.'),r),mixin:e=>(n.mixins.includes(e)?ur("Mixin has already been applied to target app"+(e.name?`: ${e.name}`:"")):n.mixins.push(e),r),component:(e,t)=>(vs(e,n.config),t?(n.components[e]&&ur(`Component "${e}" has already been registered in target app.`),n.components[e]=t,r):n.components[e]),directive:(e,t)=>(xi(e),t?(n.directives[e]&&ur(`Directive "${e}" has already been registered in target app.`),n.directives[e]=t,r):n.directives[e]),mount(){},unmount(){},provide:(e,t)=>(e in n.provides&&ur(`App already provides property with key "${String(e)}". It will be overwritten with the new value.`),n.provides[e]=t,r)};return r};function rc(e,t=null){const n="undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof my?my:void 0;n.__VUE__=!0,Dr(n.__VUE_DEVTOOLS_GLOBAL_HOOK__,n);const o=oc(e,t),r=o._context;r.config.globalProperties.$nextTick=function(e){return Ds(this.$,e)};const s=e=>(e.appContext=r,e.shapeFlag=6,e),c=function(e,t){return Gs(s(e),t)},a=function(e){return e&&nc(e.$)};return o.mount=function(){e.render=i;const t=Gs(s({type:e}),{mpType:Fs.APP,mpInstance:null,parentComponent:null,slots:[],props:null});return o._instance=t.$,function(e,t){Lr("app:init",e,t,{Fragment:ss,Text:cs,Comment:as,Static:us})}(o,js),t.$app=o,t.$createComponent=c,t.$destroyComponent=a,r.$appInstance=t,t},o.unmount=function(){ur("Cannot unmount an app.")},o}function ic(e,t,n,o){v(t)&&ui(e,t.bind(n),o)}function sc(e,t,n){!function(e,t,n){const o=e.mpType||n.$mpType;o&&"component"!==o&&Object.keys(e).forEach(o=>{if(_e(o,e[o],!1)){const r=e[o];h(r)?r.forEach(e=>ic(o,e,n,t)):ic(o,r,n,t)}})}(e,t,n)}function cc(e,t,n){return e[t]=n}function ac(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;r.proxy.$callHook(U,t)}}function uc(e,t){return e?[...new Set([].concat(e,t))]:t}let lc;const pc="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",fc=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function dc(){const e=Cn.getStorageSync("uni_id_token")||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((o=t[1],decodeURIComponent(lc(o).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""))))}catch(r){throw new Error("获取当前用户信息出错，详细错误信息为："+r.message)}var o;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}function hc(e){const t=e._context.config;var n;t.errorHandler=$e(e,ac),n=t.optionMergeStrategies,ye.forEach(e=>{n[e]=uc});const o=t.globalProperties;!function(e){e.uniIDHasRole=function(e){const{role:t}=dc();return t.indexOf(e)>-1},e.uniIDHasPermission=function(e){const{permission:t}=dc();return this.uniIDHasRole("admin")||t.indexOf(e)>-1},e.uniIDTokenValid=function(){const{tokenExpired:e}=dc();return e>Date.now()}}(o),o.$set=cc,o.$applyOptions=sc,Cn.invokeCreateVueAppHook(e)}lc="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!fc.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,o,r="",i=0;i<e.length;)t=pc.indexOf(e.charAt(i++))<<18|pc.indexOf(e.charAt(i++))<<12|(n=pc.indexOf(e.charAt(i++)))<<6|(o=pc.indexOf(e.charAt(i++))),r+=64===n?String.fromCharCode(t>>16&255):64===o?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;const gc=Object.create(null);function mc(e){delete gc[e]}function vc(e){if(!e)return;const[t,n]=e.split(",");return gc[t]?gc[t][parseInt(n)]:void 0}var yc={install(e){hc(e),e.config.globalProperties.pruneComponentPropsCache=mc;const t=e.mount;e.mount=function(n){const o=t.call(e,n),r=function(){const e="createApp";if("undefined"!=typeof global)return global[e];if("undefined"!=typeof my)return my[e]}();return r?r(o):"undefined"!=typeof createMiniProgramApp&&createMiniProgramApp(o),o}}};function bc(e,t){const n=ds||Gr,o=n.ctx,r=void 0===t||"mp-weixin"!==o.$mpPlatform&&"mp-qq"!==o.$mpPlatform||!y(t)&&"number"!=typeof t?"":"_"+t,s="e"+n.$ei+++r,c=o.$scope;if(!e)return delete c[s],s;const a=c[s];return a?a.value=e:c[s]=function(e,t){const n=e=>{var o;(o=e).type&&o.target&&(o.preventDefault=i,o.stopPropagation=i,o.stopImmediatePropagation=i,d(o,"detail")||(o.detail={}),d(o,"markerId")&&(o.detail="object"==typeof o.detail?o.detail:{},o.detail.markerId=o.markerId),k(o.detail)&&d(o.detail,"checked")&&!d(o.detail,"value")&&(o.detail.value=o.detail.checked),k(o.detail)&&(o.target=l({},o.target,o.detail)));let r=[e];e.detail&&e.detail.__args__&&(r=e.detail.__args__);const s=n.value,c=()=>hr(function(e,t){if(h(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n&&n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e(t))}return t}(e,s),t,5,r),a=e.target,u=!!a&&(!!a.dataset&&"true"===String(a.dataset.eventsync));if(!_c.includes(e.type)||u){const t=c();if("input"===e.type&&(h(t)||x(t)))return;return t}setTimeout(c)};return n.value=e,n}(e,n),s}const _c=["tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend","touchforcechange"];const xc=function(e,t=null){return e&&(e.mpType="app"),rc(e,t).use(yc)},wc=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];function $c(e,t){const n=e.ctx;n.mpType=t.mpType,n.$mpType=t.mpType,n.$mpPlatform="mp-weixin",n.$scope=t.mpInstance,n.$mp={},n._self={},e.slots={},h(t.slots)&&t.slots.length&&(t.slots.forEach(t=>{e.slots[t]=!0}),e.slots.d&&(e.slots.default=!0)),n.getOpenerEventChannel=function(){return t.mpInstance.getOpenerEventChannel()},n.$hasHook=Oc,n.$callHook=kc,e.emit=function(e,t){return function(n,...o){const r=t.$scope;if(r&&n){const e={__args__:o};r.triggerEvent(n,e)}return e.apply(this,[n,...o])}}(e.emit,n)}function Oc(e){const t=this.$[e];return!(!t||!t.length)}function kc(e,t){"mounted"===e&&(kc.call(this,"bm"),this.$.isMounted=!0,e="m");const n=this.$[e];return n&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(n,t)}const Sc=[F,N,H,J,Z,ee,te,ne,re];function Cc(e,t=new Set){if(e){Object.keys(e).forEach(n=>{_e(n,e[n])&&t.add(n)});{const{extends:n,mixins:o}=e;o&&o.forEach(e=>Cc(e,t)),n&&Cc(n,t)}}return t}function Pc(e,t,n){-1!==n.indexOf(t)||d(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.$callHook(t,e)})}const jc=[q];function Ec(e,t,n=jc){t.forEach(t=>Pc(e,t,n))}function Ac(e,t,n=jc){Cc(t).forEach(t=>Pc(e,t,n))}const Ic=fe(()=>{const e=[],t=v(getApp)&&getApp({allowDefault:!0});if(t&&t.$vm&&t.$vm.$){const n=t.$vm.$.appContext.mixins;if(h(n)){const t=Object.keys(be);n.forEach(n=>{t.forEach(t=>{d(n,t)&&!e.includes(t)&&e.push(t)})})}}return e});const Tc=[N,H,U,z,W,K];function Mc(e,t){const n=e.$,o={globalData:e.$options&&e.$options.globalData||{},$vm:e,onLaunch(t){this.$vm=e;const o=n.ctx;this.$vm&&o.$scope||($c(n,{mpType:"app",mpInstance:this,slots:[]}),o.globalData=this.globalData,e.$callHook(B,t))}};!function(e){const t=Xo(je(wx.getSystemInfoSync().language)||Pe);Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(e);const r=e.$.type;Ec(o,Tc),Ac(o,r);{const e=r.methods;e&&l(o,e)}return t&&t.parse(o),o}function Rc(e,t){if(v(e.onLaunch)){const t=wx.getLaunchOptionsSync&&wx.getLaunchOptionsSync();e.onLaunch(t)}v(e.onShow)&&wx.onAppShow&&wx.onAppShow(e=>{t.$callHook("onShow",e)}),v(e.onHide)&&wx.onAppHide&&wx.onAppHide(e=>{t.$callHook("onHide",e)})}const Vc=["externalClasses"];const Lc=/_(.*)_worklet_factory_/;function Dc(e,t){const n=e.$children;for(let r=n.length-1;r>=0;r--){const e=n[r];if(e.$scope._$vueId===t)return e}let o;for(let r=n.length-1;r>=0;r--)if(o=Dc(n[r],t),o)return o}const Nc=["eO","uR","uRIF","uI","uT","uP","uS"];function Hc(e){e.properties||(e.properties={}),l(e.properties,function(e,t=!1){const n={};return t||(Nc.forEach(e=>{n[e]={type:null,value:""}}),n.uS={type:null,value:[],observer:function(e){const t=Object.create(null);e&&e.forEach(e=>{t[e]=!0}),this.setData({$slots:t})}}),e.behaviors&&e.behaviors.includes("wx://form-field")&&(e.properties&&e.properties.name||(n.name={type:null,value:""}),e.properties&&e.properties.value||(n.value={type:null,value:""})),n}(e),function(e){const t={};return e&&e.virtualHost&&(t.virtualHostStyle={type:null,value:""},t.virtualHostClass={type:null,value:""}),t}(e.options))}const Bc=[String,Number,Boolean,Object,Array,null];function Uc(e,t){const n=function(e){return h(e)&&1===e.length?e[0]:e}(e);return-1!==Bc.indexOf(n)?n:null}function zc(e,t){return(t?function(e){const t={};k(e)&&Object.keys(e).forEach(n=>{-1===Nc.indexOf(n)&&(t[n]=e[n])});return t}(e):vc(e.uP))||{}}function Wc(e){const t=function(){const e=this.properties.uP;e&&(this.$vm?function(e,t){const n=Fo(t.props),o=vc(e)||{};Kc(n,o)&&(!function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,c=Fo(r),[a]=e.propsOptions;let u=!1;if(function(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}(e)||!(o||s>0)||16&s){let o;Ni(e,t,r,i)&&(u=!0);for(const i in c)t&&(d(t,i)||(o=T(i))!==i&&d(t,o))||(a?!n||void 0===n[i]&&void 0===n[o]||(r[i]=Hi(a,c,i,void 0,e,!0)):delete r[i]);if(i!==c)for(const e in i)t&&d(t,e)||(delete i[e],u=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(Jr(e.emitsOptions,s))continue;const l=t[s];if(a)if(d(i,s))l!==i[s]&&(i[s]=l,u=!0);else{const t=A(s);r[t]=Hi(a,c,t,l,e,!1)}else l!==i[s]&&(i[s]=l,u=!0)}}u&&qn(e,"set","$attrs"),Fi(t||{},r,e)}(t,o,n,!1),r=t.update,yr.indexOf(r)>-1&&function(e){const t=yr.indexOf(e);t>br&&yr.splice(t,1)}(t.update),t.update());var r}(e,this.$vm.$):"m"===this.properties.uT&&function(e,t){const n=t.properties,o=vc(e)||{};Kc(n,o,!1)&&t.setData(o)}(e,this))};e.observers||(e.observers={}),e.observers.uP=t}function Kc(e,t,n=!0){const o=Object.keys(t);if(n&&o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const n=o[r];if(t[n]!==e[n])return!0}return!1}function Fc(e,t){e.data={},e.behaviors=function(e){const t=e.behaviors;let n=e.props;n||(e.props=n=[]);const o=[];return h(t)&&t.forEach(e=>{o.push(e.replace("uni://","wx://")),"uni://form-field"===e&&(h(n)?(n.push("name"),n.push("modelValue")):(n.name={type:String,default:""},n.modelValue={type:[String,Number,Boolean,Array,Object,Date],default:""}))}),o}(t)}function qc(e,{parse:t,mocks:n,isPage:o,initRelation:r,handleLink:i,initLifetimes:s}){e=e.default||e;const c={multipleSlots:!0,addGlobalClass:!0,pureDataPattern:/^uP$/};h(e.mixins)&&e.mixins.forEach(e=>{_(e.options)&&l(c,e.options)}),e.options&&l(c,e.options);const a={options:c,lifetimes:s({mocks:n,isPage:o,initRelation:r,vueOptions:e}),pageLifetimes:{show(){this.$vm&&this.$vm.$callHook("onPageShow")},hide(){this.$vm&&this.$vm.$callHook("onPageHide")},resize(e){this.$vm&&this.$vm.$callHook("onPageResize",e)}},methods:{__l:i}};var u,p,f,g;return Fc(a,e),Hc(a),Wc(a),function(e,t){Vc.forEach(n=>{d(t,n)&&(e[n]=t[n])})}(a,e),u=a.methods,p=e.wxsCallMethods,h(p)&&p.forEach(e=>{u[e]=function(t){return this.$vm[e](t)}}),f=a.methods,(g=e.methods)&&Object.keys(g).forEach(e=>{const t=e.match(Lc);if(t){const n=t[1];f[e]=g[e],f[n]=g[n]}}),t&&t(a,{handleLink:i}),a}let Jc,Gc;function Yc(){return getApp().$vm}function Zc(e,t){const{parse:n,mocks:o,isPage:r,initRelation:i,handleLink:s,initLifetimes:c}=t,a=qc(e,{mocks:o,isPage:r,initRelation:i,handleLink:s,initLifetimes:c});!function({properties:e},t){h(t)?t.forEach(t=>{e[t]={type:String,value:""}}):k(t)&&Object.keys(t).forEach(n=>{const o=t[n];if(k(o)){let t=o.default;v(t)&&(t=t());const r=o.type;o.type=Uc(r),e[n]={type:o.type,value:t}}else e[n]={type:Uc(o)}})}(a,(e.default||e).props);const u=a.methods;return u.onLoad=function(e){var t;return this.options=e,this.$page={fullPath:(t=this.route+me(e),function(e){return 0===e.indexOf("/")}(t)?t:"/"+t)},this.$vm&&this.$vm.$callHook(F,e)},Ec(u,Sc),Ac(u,e),function(e,t){if(!t)return;Object.keys(be).forEach(n=>{t&be[n]&&Pc(e,n,[])})}(u,e.__runtimeHooks),Ec(u,Ic()),n&&n(a,{handleLink:s}),a}const Qc=Page,Xc=Component;function ea(e){const t=e.triggerEvent,n=function(n,...o){return t.apply(e,[(r=n,A(r.replace(pe,"-"))),...o]);var r};try{e.triggerEvent=n}catch(o){e._triggerEvent=n}}function ta(e,t,n){const o=t[e];t[e]=o?function(...e){return ea(this),o.apply(this,e)}:function(){ea(this)}}Page=function(e){return ta(F,e),Qc(e)},Component=function(e){ta("created",e);return e.properties&&e.properties.uP||(Hc(e),Wc(e)),Xc(e)};var na=Object.freeze({__proto__:null,handleLink:function(e){const t=e.detail||e.value,n=t.vuePid;let o;n&&(o=Dc(this.$vm,n)),o||(o=this.$vm),t.parent=o},initLifetimes:function({mocks:e,isPage:t,initRelation:n,vueOptions:o}){return{attached(){let r=this.properties;!function(e,t){if(!e)return;const n=e.split(","),o=n.length;1===o?t._$vueId=n[0]:2===o&&(t._$vueId=n[0],t._$vuePid=n[1])}(r.uI,this);const i={vuePid:this._$vuePid};n(this,i);const s=this,c=t(s);let a=r;this.$vm=function(e,t){Jc||(Jc=Yc().$createComponent);const n=Jc(e,t);return $s(n.$)||n}({type:o,props:zc(a,c)},{mpType:c?"page":"component",mpInstance:s,slots:r.uS||{},parentComponent:i.parent&&i.parent.$,onBeforeSetup(t,n){!function(e,t){Object.defineProperty(e,"refs",{get(){const e={};return function(e,t,n){e.selectAllComponents(t).forEach(e=>{const t=e.properties.uR;n[t]=e.$vm||e})}(t,".r",e),t.selectAllComponents(".r-i-f").forEach(t=>{const n=t.properties.uR;n&&(e[n]||(e[n]=[]),e[n].push(t.$vm||t))}),e}})}(t,s),function(e,t,n){const o=e.ctx;n.forEach(n=>{d(t,n)&&(e[n]=o[n]=t[n])})}(t,s,e),function(e,t){$c(e,t);const n=e.ctx;wc.forEach(e=>{n[e]=function(...t){const o=n.$scope;if(o&&o[e])return o[e].apply(o,t)}})}(t,n)}}),c||function(e){const t=e.$options;h(t.behaviors)&&t.behaviors.includes("uni://form-field")&&e.$watch("modelValue",()=>{e.$scope&&e.$scope.setData({name:e.name,value:e.modelValue})},{immediate:!0})}(this.$vm)},ready(){this.$vm&&(this.$vm.$callHook("mounted"),this.$vm.$callHook(q))},detached(){var e;this.$vm&&(mc(this.$vm.$.uid),e=this.$vm,Gc||(Gc=Yc().$destroyComponent),Gc(e))}}},initRelation:function(e,t){e.triggerEvent("__l",t)},isPage:function(e){return!!e.route},mocks:["__route__","__wxExparserNodeId__","__wxWebviewId__"]});const oa=function(e){return App(Mc(e,ra))};var ra;const ia=(sa=na,function(e){return Component(Zc(e,sa))});var sa;const ca=function(e){return function(t){return Component(qc(t,e))}}(na),aa=function(e){return function(t){Rc(Mc(t,e),t)}}(),ua=function(e){return function(t){const n=Mc(t,e),o=v(getApp)&&getApp({allowDefault:!0});if(!o)return;t.$.ctx.$scope=o;const r=o.globalData;r&&Object.keys(n.globalData).forEach(e=>{d(r,e)||(r[e]=n.globalData[e])}),Object.keys(n).forEach(e=>{d(o,e)||(o[e]=n[e])}),Rc(n,t)}}();wx.createApp=global.createApp=oa,wx.createPage=ia,wx.createComponent=ca,wx.createPluginApp=global.createPluginApp=aa,wx.createSubpackageApp=global.createSubpackageApp=ua,exports._export_sfc=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},exports.createSSRApp=xc,exports.e=(e,...t)=>l(e,...t),exports.f=(e,t)=>function(e,t){let n;if(h(e)||y(e)){n=new Array(e.length);for(let o=0,r=e.length;o<r;o++)n[o]=t(e[o],o,o)}else if("number"==typeof e){if(!Number.isInteger(e))return ur(`The v-for range expect an integer value but got ${e}.`),[];n=new Array(e);for(let o=0;o<e;o++)n[o]=t(o+1,o,o)}else if(_(e))if(e[Symbol.iterator])n=Array.from(e,(e,n)=>t(e,n,n));else{const o=Object.keys(e);n=new Array(o.length);for(let r=0,i=o.length;r<i;r++){const i=o[r];n[r]=t(e[i],i,r)}}else n=[];return n}(e,t),exports.index=Cn,exports.n=e=>t(e),exports.o=(e,t)=>bc(e,t),exports.t=e=>(e=>y(e)?e:null==e?"":h(e)||_(e)&&(e.toString===w||!v(e.toString))?JSON.stringify(e,n,2):String(e))(e);
