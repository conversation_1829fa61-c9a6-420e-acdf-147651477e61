"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const e=require("./common/vendor.js");Math;const t={name:"App",onLaunch:function(){this.checkLoginStatus(),this.getSystemInfo()},onShow:function(){},onHide:function(){},methods:{checkLoginStatus(){e.index.getStorageSync("token")||e.index.reLaunch({url:"/pages/login/login"})},getSystemInfo(){e.index.getSystemInfo({success:t=>{e.index.setStorageSync("systemInfo",t);const n=t.statusBarHeight||20;e.index.setStorageSync("statusBarHeight",n);const o=n+44;e.index.setStorageSync("navBarHeight",o)}})}}};const n=e._export_sfc(t,[["render",function(e,t,n,o,s,r){return{}}],["__file","/Users/<USER>/Desktop/历史脚步/test-app/miniprogram/src/App.vue"]]);function o(){const t=e.createSSRApp(n);return t.config.globalProperties.$baseUrl="https://api.healthdiet.com",{app:t}}o().app.mount("#app"),exports.createApp=o;
