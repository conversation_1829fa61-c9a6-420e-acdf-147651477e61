/* 字体图标样式文件 - 简化版本 */

.iconfont {
  font-family: "iconfont" !important;
  font-size: 32rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图标类名映射 */
.icon-heartbeat:before { content: "\e600"; }
.icon-home:before { content: "\e601"; }
.icon-utensils:before { content: "\e602"; }
.icon-search:before { content: "\e603"; }
.icon-chart-bar:before { content: "\e604"; }
.icon-chart-line:before { content: "\e605"; }
.icon-user:before { content: "\e606"; }
.icon-user-edit:before { content: "\e607"; }
.icon-phone:before { content: "\e608"; }
.icon-lock:before { content: "\e609"; }
.icon-eye:before { content: "\e60a"; }
.icon-eye-slash:before { content: "\e60b"; }
.icon-wechat:before { content: "\e60c"; }
.icon-apple:before { content: "\e60d"; }
.icon-bell:before { content: "\e60e"; }
.icon-check:before { content: "\e60f"; }
.icon-check-circle:before { content: "\e610"; }
.icon-plus:before { content: "\e611"; }
.icon-plus-circle:before { content: "\e612"; }
.icon-clock:before { content: "\e613"; }
.icon-calendar:before { content: "\e614"; }
.icon-calendar-alt:before { content: "\e615"; }
.icon-setting:before { content: "\e616"; }
.icon-cog:before { content: "\e617"; }
.icon-chevron-left:before { content: "\e618"; }
.icon-chevron-right:before { content: "\e619"; }
.icon-chevron-up:before { content: "\e61a"; }
.icon-chevron-down:before { content: "\e61b"; }
.icon-arrow-left:before { content: "\e61c"; }
.icon-arrow-right:before { content: "\e61d"; }
.icon-arrow-up:before { content: "\e61e"; }
.icon-arrow-down:before { content: "\e61f"; }
.icon-target:before { content: "\e620"; }
.icon-sun:before { content: "\e621"; }
.icon-moon:before { content: "\e622"; }
.icon-lightbulb:before { content: "\e623"; }
.icon-fire:before { content: "\e624"; }
.icon-pills:before { content: "\e625"; }
.icon-chart:before { content: "\e626"; }
.icon-chart-area:before { content: "\e627"; }
.icon-tint:before { content: "\e628"; }
.icon-weight:before { content: "\e629"; }
.icon-thermometer-half:before { content: "\e62a"; }
.icon-file-medical:before { content: "\e62b"; }
.icon-file-pdf:before { content: "\e62c"; }
.icon-file-image:before { content: "\e62d"; }
.icon-camera:before { content: "\e62e"; }
.icon-crown:before { content: "\e62f"; }
.icon-star:before { content: "\e630"; }
.icon-cloud:before { content: "\e631"; }
.icon-download:before { content: "\e632"; }
.icon-shield:before { content: "\e633"; }
.icon-shield-alt:before { content: "\e634"; }
.icon-question:before { content: "\e635"; }
.icon-question-circle:before { content: "\e636"; }
.icon-headset:before { content: "\e637"; }
.icon-info:before { content: "\e638"; }
.icon-info-circle:before { content: "\e639"; }
.icon-warning:before { content: "\e63a"; }
.icon-exclamation:before { content: "\e63b"; }
.icon-exclamation-circle:before { content: "\e63c"; }
.icon-times:before { content: "\e63d"; }
.icon-times-circle:before { content: "\e63e"; }
.icon-edit:before { content: "\e63f"; }
.icon-trash:before { content: "\e640"; }
.icon-share:before { content: "\e641"; }
.icon-copy:before { content: "\e642"; }
.icon-print:before { content: "\e643"; }
.icon-save:before { content: "\e644"; }
.icon-upload:before { content: "\e645"; }
.icon-folder:before { content: "\e646"; }
.icon-file:before { content: "\e647"; }
.icon-image:before { content: "\e648"; }
.icon-video:before { content: "\e649"; }
.icon-music:before { content: "\e64a"; }
.icon-volume-up:before { content: "\e64b"; }
.icon-volume-down:before { content: "\e64c"; }
.icon-volume-mute:before { content: "\e64d"; }
.icon-play:before { content: "\e64e"; }
.icon-pause:before { content: "\e64f"; }
.icon-stop:before { content: "\e650"; }
.icon-forward:before { content: "\e651"; }
.icon-backward:before { content: "\e652"; }
.icon-refresh:before { content: "\e653"; }
.icon-sync:before { content: "\e654"; }
.icon-loading:before { content: "\e655"; }
.icon-spinner:before { content: "\e656"; }
.icon-wifi:before { content: "\e657"; }
.icon-bluetooth:before { content: "\e658"; }
.icon-battery-full:before { content: "\e659"; }
.icon-battery-three-quarters:before { content: "\e65a"; }
.icon-battery-half:before { content: "\e65b"; }
.icon-battery-quarter:before { content: "\e65c"; }
.icon-battery-empty:before { content: "\e65d"; }
.icon-signal:before { content: "\e65e"; }
.icon-location:before { content: "\e65f"; }
.icon-map:before { content: "\e660"; }
.icon-compass:before { content: "\e661"; }
.icon-globe:before { content: "\e662"; }
.icon-link:before { content: "\e663"; }
.icon-unlink:before { content: "\e664"; }
.icon-bookmark:before { content: "\e665"; }
.icon-tag:before { content: "\e666"; }
.icon-tags:before { content: "\e667"; }
.icon-flag:before { content: "\e668"; }
.icon-heart:before { content: "\e669"; }
.icon-thumbs-up:before { content: "\e66a"; }
.icon-thumbs-down:before { content: "\e66b"; }
.icon-smile:before { content: "\e66c"; }
.icon-frown:before { content: "\e66d"; }
.icon-meh:before { content: "\e66e"; }
.icon-gift:before { content: "\e66f"; }
.icon-shopping-cart:before { content: "\e670"; }
.icon-credit-card:before { content: "\e671"; }
.icon-money:before { content: "\e672"; }
.icon-wallet:before { content: "\e673"; }
.icon-key:before { content: "\e674"; }
.icon-unlock:before { content: "\e675"; }
.icon-code:before { content: "\e676"; }
.icon-terminal:before { content: "\e677"; }
.icon-desktop:before { content: "\e678"; }
.icon-laptop:before { content: "\e679"; }
.icon-tablet:before { content: "\e67a"; }
.icon-mobile:before { content: "\e67b"; }
.icon-gamepad:before { content: "\e67c"; }
.icon-keyboard:before { content: "\e67d"; }
.icon-mouse:before { content: "\e67e"; }
.icon-printer:before { content: "\e67f"; }
.icon-server:before { content: "\e680"; }
.icon-database:before { content: "\e681"; }
.icon-hdd:before { content: "\e682"; }
.icon-usb:before { content: "\e683"; }
.icon-plug:before { content: "\e684"; }
.icon-power-off:before { content: "\e685"; }
.icon-tools:before { content: "\e686"; }
.icon-wrench:before { content: "\e687"; }
.icon-hammer:before { content: "\e688"; }
.icon-magic:before { content: "\e689"; }
.icon-puzzle-piece:before { content: "\e68a"; }
.icon-filter:before { content: "\e68b"; }
.icon-sort:before { content: "\e68c"; }
.icon-sort-up:before { content: "\e68d"; }
.icon-sort-down:before { content: "\e68e"; }
.icon-list:before { content: "\e68f"; }
.icon-grid:before { content: "\e690"; }
.icon-th:before { content: "\e691"; }
.icon-th-large:before { content: "\e692"; }
.icon-th-list:before { content: "\e693"; }
.icon-bars:before { content: "\e694"; }
.icon-ellipsis-h:before { content: "\e695"; }
.icon-ellipsis-v:before { content: "\e696"; }
.icon-more:before { content: "\e697"; }
.icon-menu:before { content: "\e698"; }
.icon-close:before { content: "\e699"; }
.icon-minimize:before { content: "\e69a"; }
.icon-maximize:before { content: "\e69b"; }
.icon-expand:before { content: "\e69c"; }
.icon-compress:before { content: "\e69d"; }
.icon-fullscreen:before { content: "\e69e"; }
.icon-fullscreen-exit:before { content: "\e69f"; }

/* 图标大小变体 */
.icon-xs { font-size: 24rpx; }
.icon-sm { font-size: 28rpx; }
.icon-md { font-size: 32rpx; }
.icon-lg { font-size: 36rpx; }
.icon-xl { font-size: 40rpx; }
.icon-2xl { font-size: 48rpx; }
.icon-3xl { font-size: 60rpx; }
.icon-4xl { font-size: 80rpx; }
.icon-5xl { font-size: 100rpx; }

/* 图标颜色变体 */
.icon-primary { color: #667eea; }
.icon-success { color: #22c55e; }
.icon-warning { color: #f59e0b; }
.icon-danger { color: #ef4444; }
.icon-info { color: #3b82f6; }
.icon-gray { color: #6b7280; }
.icon-white { color: #ffffff; }
.icon-black { color: #1f2937; }

/* 图标动画 */
.icon-spin {
  animation: icon-spin 2s infinite linear;
}

.icon-pulse {
  animation: icon-pulse 2s infinite;
}

@keyframes icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes icon-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
