<template>
  <view class="food-search-page">
    <!-- 搜索栏 -->
    <view class="search-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="search-container">
        <view class="search-input-wrapper">
          <text class="iconfont icon-search search-icon"></text>
          <input 
            type="text" 
            class="search-input"
            placeholder="搜索食物、菜品或营养成分"
            v-model="searchKeyword"
            @input="handleSearch"
            @confirm="handleSearchConfirm"
          />
        </view>
        
        <!-- 快捷筛选 -->
        <scroll-view class="filter-scroll" scroll-x>
          <view class="filter-tags">
            <view 
              class="filter-tag"
              v-for="(tag, index) in filterTags"
              :key="index"
              :class="{ 'active': tag.active }"
              @tap="toggleFilter(tag)"
            >
              <text>{{ tag.name }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content" :style="{ paddingTop: searchBarHeight + 'px' }">
      <!-- 热门搜索 -->
      <view class="hot-search card" v-if="!searchKeyword">
        <view class="section-header">
          <text class="iconfont icon-fire"></text>
          <text class="section-title">热门搜索</text>
        </view>
        
        <view class="hot-tags">
          <view 
            class="hot-tag"
            v-for="(tag, index) in hotSearchTags"
            :key="index"
            @tap="searchHotTag(tag)"
          >
            <text>{{ tag }}</text>
          </view>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view class="search-results" v-if="searchKeyword">
        <view class="result-header">
          <text class="result-count">找到 {{ foodList.length }} 个结果</text>
        </view>
        
        <view class="food-list">
          <view 
            class="food-item"
            v-for="(food, index) in foodList"
            :key="index"
            :class="food.safetyClass"
            @tap="viewFoodDetail(food)"
          >
            <image class="food-image" :src="food.image" mode="aspectFill"></image>
            <view class="food-info">
              <view class="food-basic">
                <text class="food-name">{{ food.name }}</text>
                <text class="food-unit">每{{ food.unit }}</text>
                <view class="food-tags">
                  <text class="safety-tag" :class="food.safetyClass">{{ food.safetyText }}</text>
                  <text class="feature-tag" v-if="food.feature">{{ food.feature }}</text>
                </view>
              </view>
            </view>
            <view class="food-nutrition">
              <text class="calories">{{ food.calories }} kcal</text>
              <text class="carbs">碳水 {{ food.carbs }}g</text>
              <text class="sugar" v-if="food.sugar">糖分 {{ food.sugar }}g</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 推荐食物列表 -->
      <view class="recommended-foods" v-if="!searchKeyword">
        <view class="section-header">
          <text class="section-title">为您推荐</text>
        </view>
        
        <view class="food-list">
          <view 
            class="food-item"
            v-for="(food, index) in recommendedFoods"
            :key="index"
            :class="food.safetyClass"
            @tap="viewFoodDetail(food)"
          >
            <image class="food-image" :src="food.image" mode="aspectFill"></image>
            <view class="food-info">
              <view class="food-basic">
                <text class="food-name">{{ food.name }}</text>
                <text class="food-unit">每{{ food.unit }}</text>
                <view class="food-tags">
                  <text class="safety-tag" :class="food.safetyClass">{{ food.safetyText }}</text>
                  <text class="feature-tag" v-if="food.feature">{{ food.feature }}</text>
                </view>
              </view>
            </view>
            <view class="food-nutrition">
              <text class="calories">{{ food.calories }} kcal</text>
              <text class="carbs">碳水 {{ food.carbs }}g</text>
              <text class="sugar" v-if="food.sugar">糖分 {{ food.sugar }}g</text>
            </view>
            
            <view class="food-actions">
              <view class="safety-info">
                <text class="safety-desc" :class="food.safetyClass">{{ food.safetyDesc }}</text>
              </view>
              <text class="view-detail">查看详情</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FoodSearch',
  data() {
    return {
      statusBarHeight: 20,
      searchBarHeight: 160,
      searchKeyword: '',
      filterTags: [
        { name: '全部', active: true, type: 'all' },
        { name: '推荐食物', active: false, type: 'recommended' },
        { name: '谨慎食用', active: false, type: 'caution' },
        { name: '避免食用', active: false, type: 'avoid' },
        { name: '低糖食物', active: false, type: 'low-sugar' }
      ],
      hotSearchTags: ['苹果', '鸡胸肉', '燕麦', '西兰花', '三文鱼', '糙米'],
      foodList: [],
      recommendedFoods: [
        {
          id: 1,
          name: '苹果',
          unit: '100g',
          calories: 52,
          carbs: 14,
          sugar: 10,
          image: '/static/images/apple.jpg',
          safetyClass: 'safe',
          safetyText: '推荐',
          safetyDesc: '✓ 适合糖尿病患者',
          feature: '低糖水果'
        },
        {
          id: 2,
          name: '香蕉',
          unit: '100g',
          calories: 89,
          carbs: 23,
          sugar: 12,
          image: '/static/images/banana.jpg',
          safetyClass: 'caution',
          safetyText: '谨慎',
          safetyDesc: '⚠ 建议少量食用',
          feature: '中等糖分'
        },
        {
          id: 3,
          name: '蛋糕',
          unit: '100g',
          calories: 347,
          carbs: 46,
          sugar: 35,
          image: '/static/images/cake.jpg',
          safetyClass: 'avoid',
          safetyText: '避免',
          safetyDesc: '✗ 不建议食用',
          feature: '高糖高脂'
        },
        {
          id: 4,
          name: '西兰花',
          unit: '100g',
          calories: 34,
          carbs: 7,
          image: '/static/images/broccoli.jpg',
          safetyClass: 'safe',
          safetyText: '推荐',
          safetyDesc: '✓ 富含维生素C',
          feature: '低卡蔬菜'
        }
      ]
    }
  },
  onLoad() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.statusBarHeight = uni.getStorageSync('statusBarHeight') || 20
    },
    
    handleSearch(e) {
      const keyword = e.detail.value
      this.searchKeyword = keyword
      
      if (keyword) {
        this.performSearch(keyword)
      } else {
        this.foodList = []
      }
    },
    
    handleSearchConfirm(e) {
      const keyword = e.detail.value
      if (keyword) {
        this.performSearch(keyword)
      }
    },
    
    performSearch(keyword) {
      // 模拟搜索
      const allFoods = [...this.recommendedFoods]
      this.foodList = allFoods.filter(food => 
        food.name.includes(keyword) || 
        food.feature?.includes(keyword)
      )
    },
    
    searchHotTag(tag) {
      this.searchKeyword = tag
      this.performSearch(tag)
    },
    
    toggleFilter(tag) {
      // 重置所有标签
      this.filterTags.forEach(t => t.active = false)
      // 激活当前标签
      tag.active = true
      
      // 根据筛选条件过滤食物
      this.filterFoods(tag.type)
    },
    
    filterFoods(type) {
      if (type === 'all') {
        this.foodList = []
        return
      }
      
      let filtered = []
      switch (type) {
        case 'recommended':
          filtered = this.recommendedFoods.filter(food => food.safetyClass === 'safe')
          break
        case 'caution':
          filtered = this.recommendedFoods.filter(food => food.safetyClass === 'caution')
          break
        case 'avoid':
          filtered = this.recommendedFoods.filter(food => food.safetyClass === 'avoid')
          break
        case 'low-sugar':
          filtered = this.recommendedFoods.filter(food => food.sugar && food.sugar <= 10)
          break
        default:
          filtered = this.recommendedFoods
      }
      
      this.foodList = filtered
      this.searchKeyword = '筛选结果'
    },
    
    viewFoodDetail(food) {
      uni.showToast({
        title: `查看${food.name}详情`,
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.food-search-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.search-container {
  padding: 32rpx;
}

.search-input-wrapper {
  position: relative;
  margin-bottom: 24rpx;
}

.search-icon {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #9ca3af;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 24rpx 24rpx 24rpx 72rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 24rpx;
  background: white;
  font-size: 32rpx;
  color: #1f2937;

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  }
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: flex;
  gap: 16rpx;
}

.filter-tag {
  flex-shrink: 0;
  padding: 16rpx 32rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  background: #f3f4f6;
  color: #6b7280;

  &.active {
    background: #667eea;
    color: white;
  }
}

.page-content {
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;

  .iconfont {
    font-size: 36rpx;
    color: #ef4444;
    margin-right: 16rpx;
  }
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-tag {
  padding: 16rpx 24rpx;
  background: #f3f4f6;
  color: #374151;
  border-radius: 48rpx;
  font-size: 28rpx;
}

.result-header {
  margin-bottom: 32rpx;
}

.result-count {
  font-size: 28rpx;
  color: #6b7280;
}

.food-list {
  .food-item {
    background: white;
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4rpx);
      box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
    }

    &.safe {
      border-left: 8rpx solid #22c55e;
    }

    &.caution {
      border-left: 8rpx solid #f59e0b;
    }

    &.avoid {
      border-left: 8rpx solid #ef4444;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.food-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.food-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.food-info {
  flex: 1;
}

.food-basic {
  margin-bottom: 16rpx;
}

.food-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}

.food-unit {
  font-size: 28rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 16rpx;
}

.food-tags {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.safety-tag {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;

  &.safe {
    background: #dcfce7;
    color: #22c55e;
  }

  &.caution {
    background: #fef3c7;
    color: #f59e0b;
  }

  &.avoid {
    background: #fee2e2;
    color: #ef4444;
  }
}

.feature-tag {
  font-size: 24rpx;
  color: #9ca3af;
}

.food-nutrition {
  text-align: right;
  flex-shrink: 0;
}

.calories {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}

.carbs, .sugar {
  font-size: 24rpx;
  color: #9ca3af;
  display: block;
  margin-bottom: 4rpx;
}

.food-actions {
  width: 100%;
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.safety-info {
  flex: 1;
}

.safety-desc {
  font-size: 28rpx;

  &.safe {
    color: #22c55e;
  }

  &.caution {
    color: #f59e0b;
  }

  &.avoid {
    color: #ef4444;
  }
}

.view-detail {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
}
</style>
