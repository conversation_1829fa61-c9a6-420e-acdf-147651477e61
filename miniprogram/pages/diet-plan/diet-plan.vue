<template>
  <view class="diet-plan-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content gradient-primary">
        <view class="navbar-header">
          <text class="navbar-title">饮食计划</text>
          <view class="navbar-actions">
            <text class="iconfont icon-calendar"></text>
            <text class="iconfont icon-setting"></text>
          </view>
        </view>
        
        <!-- 日期选择器 -->
        <view class="date-selector">
          <view class="date-navigation">
            <text class="iconfont icon-chevron-left" @tap="previousDay"></text>
            <text class="current-date">{{ currentDateText }}</text>
            <text class="iconfont icon-chevron-right" @tap="nextDay"></text>
          </view>
          
          <!-- 周视图 -->
          <view class="week-view">
            <view 
              class="week-day" 
              v-for="(day, index) in weekDays" 
              :key="index"
              :class="{ 'active': day.isToday }"
              @tap="selectDate(day)"
            >
              <text class="day-label">{{ day.label }}</text>
              <view class="day-number">
                <text>{{ day.number }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content" :style="{ paddingTop: navBarHeight + 'px' }">
      <!-- 今日营养目标 -->
      <view class="nutrition-target card">
        <view class="card-header">
          <text class="iconfont icon-target"></text>
          <text class="section-title">今日营养目标</text>
        </view>
        
        <view class="target-grid">
          <view class="target-item" v-for="(item, index) in nutritionTargets" :key="index">
            <view class="progress-circle">
              <view class="circle-bg">
                <view 
                  class="circle-fill" 
                  :class="item.colorClass"
                  :style="{ 
                    background: `conic-gradient(${item.color} ${item.percentage * 3.6}deg, #e5e7eb 0deg)` 
                  }"
                ></view>
                <view class="circle-content">
                  <text class="percentage">{{ item.percentage }}%</text>
                </view>
              </view>
            </view>
            <text class="target-label">{{ item.name }}</text>
            <text class="target-value">{{ item.current }}/{{ item.target }}</text>
          </view>
        </view>
      </view>

      <!-- 早餐 -->
      <view class="meal-card card">
        <view class="meal-header">
          <view class="meal-title">
            <text class="iconfont icon-sun meal-icon"></text>
            <text class="meal-name">早餐</text>
          </view>
          <view class="meal-status">
            <text class="status-badge completed">已完成</text>
            <text class="iconfont icon-check-circle"></text>
          </view>
        </view>
        
        <view class="food-list">
          <view class="food-item" v-for="(food, index) in breakfastFoods" :key="index">
            <image class="food-image" :src="food.image" mode="aspectFill"></image>
            <view class="food-info">
              <text class="food-name">{{ food.name }}</text>
              <text class="food-amount">{{ food.amount }}</text>
            </view>
            <view class="food-nutrition">
              <text class="calories">{{ food.calories }} kcal</text>
              <text class="carbs">碳水 {{ food.carbs }}g</text>
            </view>
          </view>
        </view>
        
        <view class="meal-summary">
          <text class="summary-label">总热量</text>
          <text class="summary-value">418 kcal</text>
        </view>
      </view>

      <!-- 午餐 -->
      <view class="meal-card card">
        <view class="meal-header">
          <view class="meal-title">
            <text class="iconfont icon-sun meal-icon orange"></text>
            <text class="meal-name">午餐</text>
          </view>
          <view class="meal-status">
            <text class="status-badge pending">待记录</text>
            <text class="iconfont icon-plus-circle" @tap="addMeal('lunch')"></text>
          </view>
        </view>
        
        <!-- AI推荐菜单 -->
        <view class="ai-recommendation">
          <view class="recommendation-header">
            <text class="iconfont icon-lightbulb"></text>
            <text class="recommendation-title">AI推荐菜单</text>
          </view>
          
          <view class="recommendation-list">
            <view class="recommendation-item" v-for="(item, index) in lunchRecommendations" :key="index">
              <text class="item-name">{{ item.name }}</text>
              <text class="item-calories">{{ item.calories }} kcal</text>
            </view>
          </view>
          
          <button class="adopt-btn" @tap="adoptRecommendation">
            采用推荐菜单
          </button>
        </view>
      </view>

      <!-- 晚餐 -->
      <view class="meal-card card">
        <view class="meal-header">
          <view class="meal-title">
            <text class="iconfont icon-moon meal-icon blue"></text>
            <text class="meal-name">晚餐</text>
          </view>
          <view class="meal-status">
            <text class="status-badge inactive">未开始</text>
            <text class="iconfont icon-plus-circle inactive" @tap="addMeal('dinner')"></text>
          </view>
        </view>
        
        <view class="meal-placeholder">
          <text class="iconfont icon-utensils placeholder-icon"></text>
          <text class="placeholder-text">晚餐时间还未到</text>
          <text class="placeholder-subtitle">建议在18:00-19:00之间用餐</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DietPlan',
  data() {
    return {
      statusBarHeight: 20,
      navBarHeight: 64,
      currentDate: new Date(),
      nutritionTargets: [
        {
          name: '热量',
          current: 680,
          target: 1800,
          percentage: 38,
          color: '#3b82f6',
          colorClass: 'blue'
        },
        {
          name: '蛋白质',
          current: 28,
          target: 75,
          percentage: 38,
          color: '#22c55e',
          colorClass: 'green'
        },
        {
          name: '脂肪',
          current: 12,
          target: 60,
          percentage: 20,
          color: '#f59e0b',
          colorClass: 'yellow'
        }
      ],
      breakfastFoods: [
        {
          name: '燕麦粥',
          amount: '100g',
          calories: 389,
          carbs: 66,
          image: '/static/images/oatmeal.jpg'
        },
        {
          name: '蓝莓',
          amount: '50g',
          calories: 29,
          carbs: 7,
          image: '/static/images/blueberry.jpg'
        }
      ],
      lunchRecommendations: [
        {
          name: '糙米饭 (100g)',
          calories: 111
        },
        {
          name: '清蒸鲈鱼 (150g)',
          calories: 125
        },
        {
          name: '菠菜豆腐汤 (200ml)',
          calories: 45
        }
      ]
    }
  },
  computed: {
    currentDateText() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth() + 1
      const date = this.currentDate.getDate()
      return `${year}年${month}月${date}日`
    },
    
    weekDays() {
      const days = []
      const today = new Date(this.currentDate)
      const dayOfWeek = today.getDay()
      const startOfWeek = new Date(today)
      startOfWeek.setDate(today.getDate() - dayOfWeek + 1)
      
      const weekLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      
      for (let i = 0; i < 7; i++) {
        const day = new Date(startOfWeek)
        day.setDate(startOfWeek.getDate() + i)
        
        days.push({
          label: weekLabels[i],
          number: day.getDate(),
          date: new Date(day),
          isToday: this.isSameDay(day, this.currentDate)
        })
      }
      
      return days
    }
  },
  onLoad() {
    this.initPage()
  },
  methods: {
    initPage() {
      this.statusBarHeight = uni.getStorageSync('statusBarHeight') || 20
      this.navBarHeight = uni.getStorageSync('navBarHeight') || 64
    },
    
    isSameDay(date1, date2) {
      return date1.getFullYear() === date2.getFullYear() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getDate() === date2.getDate()
    },
    
    previousDay() {
      const newDate = new Date(this.currentDate)
      newDate.setDate(newDate.getDate() - 1)
      this.currentDate = newDate
    },
    
    nextDay() {
      const newDate = new Date(this.currentDate)
      newDate.setDate(newDate.getDate() + 1)
      this.currentDate = newDate
    },
    
    selectDate(day) {
      this.currentDate = day.date
    },
    
    addMeal(mealType) {
      uni.showToast({
        title: '添加' + (mealType === 'lunch' ? '午餐' : '晚餐'),
        icon: 'none'
      })
    },
    
    adoptRecommendation() {
      uni.showModal({
        title: '采用推荐菜单',
        content: '确定要采用AI推荐的午餐菜单吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '已添加到午餐计划',
              icon: 'success'
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.diet-plan-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.navbar-content {
  padding: 32rpx;
  color: white;
}

.navbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.navbar-title {
  font-size: 40rpx;
  font-weight: bold;
}

.navbar-actions {
  display: flex;
  gap: 24rpx;

  .iconfont {
    font-size: 36rpx;
  }
}

.date-selector {
  .date-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .iconfont {
      font-size: 32rpx;
      opacity: 0.8;
    }
  }

  .current-date {
    font-size: 36rpx;
    font-weight: 600;
  }
}

.week-view {
  display: flex;
  justify-content: space-between;
}

.week-day {
  text-align: center;

  &.active .day-number {
    background: white;

    text {
      color: #667eea;
      font-weight: bold;
    }
  }
}

.day-label {
  font-size: 24rpx;
  opacity: 0.8;
  display: block;
  margin-bottom: 8rpx;
}

.day-number {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;

  text {
    font-size: 28rpx;
  }
}

.page-content {
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;

  .iconfont {
    font-size: 36rpx;
    color: #22c55e;
    margin-right: 16rpx;
  }
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.target-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
}

.target-item {
  text-align: center;
}

.progress-circle {
  margin-bottom: 16rpx;
}

.circle-bg {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  position: relative;
  margin: 0 auto;
}

.circle-fill {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
}

.circle-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.percentage {
  font-size: 24rpx;
  font-weight: bold;
  color: #1f2937;
}

.target-label {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 4rpx;
}

.target-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
}

.meal-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
  }
}

.meal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.meal-title {
  display: flex;
  align-items: center;
}

.meal-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
  color: #f59e0b;

  &.orange {
    color: #f97316;
  }

  &.blue {
    color: #3b82f6;
  }
}

.meal-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.meal-status {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.status-badge {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;

  &.completed {
    background: #dcfce7;
    color: #22c55e;
  }

  &.pending {
    background: #fef3c7;
    color: #f59e0b;
  }

  &.inactive {
    background: #f3f4f6;
    color: #9ca3af;
  }
}

.iconfont {
  font-size: 32rpx;

  &.icon-check-circle {
    color: #22c55e;
  }

  &.icon-plus-circle {
    color: #667eea;

    &.inactive {
      color: #9ca3af;
    }
  }
}

.food-list {
  margin-bottom: 32rpx;
}

.food-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.food-image {
  width: 96rpx;
  height: 96rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}

.food-info {
  flex: 1;
}

.food-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}

.food-amount {
  font-size: 28rpx;
  color: #6b7280;
  display: block;
}

.food-nutrition {
  text-align: right;
}

.calories {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 4rpx;
}

.carbs {
  font-size: 24rpx;
  color: #9ca3af;
  display: block;
}

.meal-summary {
  display: flex;
  justify-content: space-between;
  padding-top: 24rpx;
  border-top: 1rpx solid #e5e7eb;
}

.summary-label {
  font-size: 28rpx;
  color: #6b7280;
}

.summary-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.ai-recommendation {
  background: #f8f4ff;
  border-radius: 16rpx;
  padding: 32rpx;
}

.recommendation-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;

  .iconfont {
    font-size: 32rpx;
    color: #8b5cf6;
    margin-right: 16rpx;
  }
}

.recommendation-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #7c3aed;
}

.recommendation-list {
  margin-bottom: 24rpx;
}

.recommendation-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-name {
  font-size: 28rpx;
  color: #374151;
}

.item-calories {
  font-size: 28rpx;
  color: #6b7280;
}

.adopt-btn {
  width: 100%;
  padding: 24rpx;
  background: #8b5cf6;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.meal-placeholder {
  text-align: center;
  padding: 64rpx 32rpx;
}

.placeholder-icon {
  font-size: 80rpx;
  color: #d1d5db;
  display: block;
  margin-bottom: 24rpx;
}

.placeholder-text {
  font-size: 32rpx;
  color: #9ca3af;
  display: block;
  margin-bottom: 8rpx;
}

.placeholder-subtitle {
  font-size: 28rpx;
  color: #d1d5db;
  display: block;
}
</style>
