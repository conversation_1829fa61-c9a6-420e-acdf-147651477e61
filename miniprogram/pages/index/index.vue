<template>
  <view class="page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content gradient-primary">
        <!-- 顶部问候区域 -->
        <view class="greeting-section">
          <view class="greeting-content">
            <view class="greeting-text">
              <text class="greeting-title">{{ greetingText }}，{{ userInfo.name || '用户' }}</text>
              <text class="greeting-subtitle">今天是您健康管理的第{{ managementDays }}天</text>
            </view>
            <view class="notification-btn" @tap="handleNotification">
              <text class="iconfont icon-bell"></text>
            </view>
          </view>
          
          <!-- 今日健康评分 -->
          <view class="health-score-card gradient-success">
            <view class="score-content">
              <view class="score-info">
                <text class="score-label">今日健康评分</text>
                <view class="score-value">
                  <text class="score-number">{{ healthScore }}</text>
                  <text class="score-unit">分</text>
                  <text class="score-change">↑ {{ scoreChange }}分</text>
                </view>
              </view>
              <view class="score-icon">
                <text class="iconfont icon-heart"></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content" :style="{ paddingTop: navBarHeight + 'px' }">
      <!-- 今日提醒 -->
      <view class="reminder-card gradient-warning" v-if="hasReminders">
        <view class="reminder-content">
          <view class="reminder-info">
            <text class="iconfont icon-warning"></text>
            <text class="reminder-title">重要提醒</text>
          </view>
          <text class="reminder-text">您有{{ reminderCount }}个用药提醒和{{ appointmentCount }}个体检预约</text>
          <view class="reminder-btn" @tap="viewReminders">
            <text>查看详情</text>
          </view>
        </view>
      </view>

      <!-- 今日饮食概览 -->
      <view class="diet-overview card">
        <view class="card-header">
          <view class="section-title">
            <text class="iconfont icon-utensils"></text>
            <text class="title-text">今日饮食</text>
          </view>
          <text class="view-detail" @tap="viewDietDetail">查看详情</text>
        </view>
        
        <view class="meal-status">
          <view class="meal-item" v-for="(meal, index) in mealStatus" :key="index">
            <view class="meal-icon" :class="meal.statusClass">
              <text class="iconfont" :class="meal.icon"></text>
            </view>
            <text class="meal-name">{{ meal.name }}</text>
            <text class="meal-status-text">{{ meal.status }}</text>
          </view>
        </view>
        
        <!-- 营养摄入进度 -->
        <view class="nutrition-progress">
          <view class="progress-item" v-for="(item, index) in nutritionProgress" :key="index">
            <view class="progress-header">
              <text class="progress-label">{{ item.name }}</text>
              <text class="progress-value">{{ item.current }}/{{ item.target }} {{ item.unit }}</text>
            </view>
            <view class="progress-bar">
              <view class="progress-fill" :class="item.colorClass" :style="{ width: item.percentage + '%' }"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 快捷功能 -->
      <view class="quick-actions card">
        <view class="section-title">
          <text class="title-text">快捷功能</text>
        </view>
        <view class="action-grid">
          <view class="action-item" v-for="(action, index) in quickActions" :key="index" @tap="handleQuickAction(action)">
            <view class="action-icon" :class="action.bgClass">
              <text class="iconfont" :class="action.icon"></text>
            </view>
            <text class="action-name">{{ action.name }}</text>
          </view>
        </view>
      </view>

      <!-- 健康趋势 -->
      <view class="health-trend card">
        <view class="card-header">
          <view class="section-title">
            <text class="title-text">健康趋势</text>
          </view>
          <text class="view-detail" @tap="viewHealthTrend">查看更多</text>
        </view>
        
        <view class="trend-grid">
          <view class="trend-item" v-for="(item, index) in healthTrends" :key="index">
            <view class="trend-content">
              <view class="trend-header">
                <text class="trend-label">{{ item.name }}</text>
                <text class="iconfont" :class="item.trendIcon" :style="{ color: item.trendColor }"></text>
              </view>
              <text class="trend-value">{{ item.value }}</text>
              <text class="trend-unit">{{ item.unit }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Index',
  data() {
    return {
      statusBarHeight: 20,
      navBarHeight: 64,
      managementDays: 32,
      healthScore: 85,
      scoreChange: 5,
      reminderCount: 2,
      appointmentCount: 1,
      userInfo: {
        name: '张先生'
      },
      mealStatus: [
        {
          name: '早餐',
          status: '已记录',
          icon: 'icon-check',
          statusClass: 'status-completed'
        },
        {
          name: '午餐',
          status: '待记录',
          icon: 'icon-clock',
          statusClass: 'status-pending'
        },
        {
          name: '晚餐',
          status: '未开始',
          icon: 'icon-plus',
          statusClass: 'status-inactive'
        }
      ],
      nutritionProgress: [
        {
          name: '热量',
          current: 680,
          target: 1800,
          unit: 'kcal',
          percentage: 38,
          colorClass: 'progress-blue'
        },
        {
          name: '碳水化合物',
          current: 85,
          target: 225,
          unit: 'g',
          percentage: 38,
          colorClass: 'progress-green'
        }
      ],
      quickActions: [
        {
          name: '食物查询',
          icon: 'icon-search',
          bgClass: 'bg-blue',
          action: 'search'
        },
        {
          name: '记录饮食',
          icon: 'icon-plus',
          bgClass: 'bg-green',
          action: 'record'
        },
        {
          name: '用药提醒',
          icon: 'icon-pills',
          bgClass: 'bg-purple',
          action: 'medicine'
        },
        {
          name: '健康报告',
          icon: 'icon-chart',
          bgClass: 'bg-red',
          action: 'report'
        }
      ],
      healthTrends: [
        {
          name: '血糖',
          value: '6.8',
          unit: 'mmol/L',
          trendIcon: 'icon-arrow-up',
          trendColor: '#ef4444'
        },
        {
          name: '体重',
          value: '68.2',
          unit: 'kg',
          trendIcon: 'icon-arrow-down',
          trendColor: '#22c55e'
        }
      ]
    }
  },
  computed: {
    greetingText() {
      const hour = new Date().getHours()
      if (hour < 12) return '早上好'
      if (hour < 18) return '下午好'
      return '晚上好'
    },
    hasReminders() {
      return this.reminderCount > 0 || this.appointmentCount > 0
    }
  },
  onLoad() {
    this.initPage()
  },
  onShow() {
    this.refreshData()
  },
  methods: {
    initPage() {
      // 获取状态栏高度
      this.statusBarHeight = uni.getStorageSync('statusBarHeight') || 20
      this.navBarHeight = uni.getStorageSync('navBarHeight') || 64
      
      // 加载用户数据
      this.loadUserData()
    },
    
    loadUserData() {
      // 模拟加载用户数据
      const userData = uni.getStorageSync('userData')
      if (userData) {
        this.userInfo = userData
      }
    },
    
    refreshData() {
      // 刷新页面数据
      this.loadHealthData()
      this.loadDietData()
    },
    
    loadHealthData() {
      // 模拟加载健康数据
      console.log('加载健康数据')
    },
    
    loadDietData() {
      // 模拟加载饮食数据
      console.log('加载饮食数据')
    },
    
    handleNotification() {
      uni.showToast({
        title: '暂无新通知',
        icon: 'none'
      })
    },
    
    viewReminders() {
      uni.navigateTo({
        url: '/pages/health-management/health-management'
      })
    },
    
    viewDietDetail() {
      uni.switchTab({
        url: '/pages/diet-plan/diet-plan'
      })
    },
    
    viewHealthTrend() {
      uni.switchTab({
        url: '/pages/health-management/health-management'
      })
    },
    
    handleQuickAction(action) {
      switch (action.action) {
        case 'search':
          uni.switchTab({
            url: '/pages/food-search/food-search'
          })
          break
        case 'record':
          uni.switchTab({
            url: '/pages/diet-plan/diet-plan'
          })
          break
        case 'medicine':
          uni.switchTab({
            url: '/pages/health-management/health-management'
          })
          break
        case 'report':
          uni.switchTab({
            url: '/pages/health-management/health-management'
          })
          break
        default:
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.navbar-content {
  padding: 32rpx;
  border-radius: 0 0 48rpx 48rpx;
}

.greeting-section {
  color: white;
}

.greeting-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.greeting-title {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.greeting-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}

.notification-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .iconfont {
    font-size: 36rpx;
    color: white;
  }
}

.health-score-card {
  border-radius: 32rpx;
  padding: 32rpx;
}

.score-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.score-label {
  color: white;
  font-weight: 600;
  font-size: 32rpx;
  display: block;
  margin-bottom: 8rpx;
}

.score-value {
  display: flex;
  align-items: baseline;
}

.score-number {
  font-size: 60rpx;
  font-weight: bold;
  color: white;
}

.score-unit {
  font-size: 24rpx;
  color: white;
  margin-left: 8rpx;
}

.score-change {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 16rpx;
}

.score-icon {
  width: 100rpx;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .iconfont {
    font-size: 48rpx;
    color: white;
  }
}

.page-content {
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.reminder-card {
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  color: white;
}

.reminder-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  
  .iconfont {
    font-size: 32rpx;
    margin-right: 16rpx;
  }
}

.reminder-title {
  font-weight: 600;
  font-size: 32rpx;
}

.reminder-text {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 24rpx;
  display: block;
}

.reminder-btn {
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 32rpx;
  border-radius: 16rpx;
  display: inline-block;
  font-size: 28rpx;
}

.card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  
  .iconfont {
    font-size: 36rpx;
    color: #667eea;
    margin-right: 16rpx;
  }
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.view-detail {
  font-size: 28rpx;
  color: #667eea;
}

.meal-status {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.meal-item {
  text-align: center;
  flex: 1;
}

.meal-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16rpx;
  
  .iconfont {
    font-size: 32rpx;
  }
  
  &.status-completed {
    background: #dcfce7;
    
    .iconfont {
      color: #22c55e;
    }
  }
  
  &.status-pending {
    background: #fef3c7;
    
    .iconfont {
      color: #f59e0b;
    }
  }
  
  &.status-inactive {
    background: #f3f4f6;
    
    .iconfont {
      color: #9ca3af;
    }
  }
}

.meal-name {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 4rpx;
}

.meal-status-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
}

.nutrition-progress {
  .progress-item {
    margin-bottom: 24rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 28rpx;
  color: #6b7280;
}

.progress-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
  
  &.progress-blue {
    background: #3b82f6;
  }
  
  &.progress-green {
    background: #22c55e;
  }
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

.action-item {
  text-align: center;
}

.action-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16rpx;
  
  .iconfont {
    font-size: 40rpx;
    color: white;
  }
  
  &.bg-blue {
    background: #dbeafe;
    
    .iconfont {
      color: #3b82f6;
    }
  }
  
  &.bg-green {
    background: #dcfce7;
    
    .iconfont {
      color: #22c55e;
    }
  }
  
  &.bg-purple {
    background: #ede9fe;
    
    .iconfont {
      color: #8b5cf6;
    }
  }
  
  &.bg-red {
    background: #fee2e2;
    
    .iconfont {
      color: #ef4444;
    }
  }
}

.action-name {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
}

.trend-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.trend-item {
  background: #f9fafb;
  border-radius: 24rpx;
  padding: 32rpx;
  text-align: center;
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.trend-label {
  font-size: 28rpx;
  color: #6b7280;
}

.trend-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 4rpx;
}

.trend-unit {
  font-size: 24rpx;
  color: #9ca3af;
  display: block;
}
</style>
