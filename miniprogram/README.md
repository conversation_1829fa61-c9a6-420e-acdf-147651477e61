# 健康饮食管理微信小程序

专业的慢性病饮食管理助手，基于 uni-app + Vue3 开发的微信小程序。

## 项目简介

这是一个专为慢性病患者设计的饮食管理应用，提供个性化的饮食方案、食物查询、健康数据管理等功能，帮助用户通过科学的饮食管理改善健康状况。

## 功能特色

### 🏠 首页
- 健康评分展示
- 今日饮食概览
- 营养摄入进度
- 快捷功能入口
- 健康趋势图表

### 🍽️ 饮食计划
- 日历式饮食规划
- 三餐营养目标管理
- AI智能推荐菜单
- 营养成分可视化
- 饮食记录与分析

### 🔍 食物查询
- 智能搜索功能
- 食物安全性分级
- 营养成分详情
- 个性化推荐
- 热门搜索标签

### 📊 健康管理
- 用药提醒管理
- 健康指标录入
- 体检报告上传
- 健康趋势分析
- 智能拍照识别

### 👤 个人中心
- 用户资料管理
- VIP会员服务
- 数据同步备份
- 隐私设置
- 客服支持

## 技术栈

- **框架**: uni-app 3.x
- **前端**: Vue 3 + Composition API
- **样式**: SCSS + 原子化CSS
- **构建**: Vite
- **图标**: 自定义字体图标
- **平台**: 微信小程序

## 项目结构

```
miniprogram/
├── pages/                  # 页面文件
│   ├── index/              # 首页
│   ├── login/              # 登录页
│   ├── register/           # 注册页
│   ├── health-profile/     # 健康档案
│   ├── diet-plan/          # 饮食计划
│   ├── food-search/        # 食物查询
│   ├── health-management/  # 健康管理
│   ├── profile/            # 个人中心
│   └── vip-subscription/   # VIP订购
├── static/                 # 静态资源
│   ├── images/             # 图片资源
│   ├── fonts/              # 字体文件
│   ├── styles/             # 全局样式
│   └── tabbar/             # 底部导航图标
├── components/             # 公共组件
├── utils/                  # 工具函数
├── api/                    # API接口
├── store/                  # 状态管理
├── App.vue                 # 应用入口
├── main.js                 # 主入口文件
├── pages.json              # 页面配置
├── manifest.json           # 应用配置
└── package.json            # 依赖配置
```

## 开发指南

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0
- 微信开发者工具

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
# 微信小程序开发
npm run dev:mp-weixin

# H5开发
npm run dev:h5
```

### 构建发布

```bash
# 构建微信小程序
npm run build:mp-weixin

# 构建H5
npm run build:h5
```

### 代码规范

```bash
# 代码检查
npm run lint

# 自动修复
npm run lint:fix
```

## 页面说明

### 登录注册流程
1. **登录页** (`/pages/login/login`) - 支持手机号密码登录、第三方登录
2. **注册页** (`/pages/register/register`) - 手机验证码注册
3. **健康档案** (`/pages/health-profile/health-profile`) - 首次注册完善健康信息

### 主要功能页面
1. **首页** (`/pages/index/index`) - 健康概览和快捷入口
2. **饮食计划** (`/pages/diet-plan/diet-plan`) - 饮食规划和营养管理
3. **食物查询** (`/pages/food-search/food-search`) - 食物搜索和营养查询
4. **健康管理** (`/pages/health-management/health-management`) - 健康数据管理
5. **个人中心** (`/pages/profile/profile`) - 用户设置和账户管理

### 商业化页面
1. **VIP订购** (`/pages/vip-subscription/vip-subscription`) - 会员服务购买

## 设计规范

### 色彩系统
- **主色调**: #667eea (渐变紫)
- **成功色**: #22c55e (绿色)
- **警告色**: #f59e0b (橙色)
- **危险色**: #ef4444 (红色)
- **金色**: #fbbf24 (VIP专用)

### 字体规范
- **标题**: 36rpx - 60rpx
- **正文**: 28rpx - 32rpx
- **辅助**: 24rpx - 26rpx

### 间距规范
- **基础单位**: 8rpx
- **常用间距**: 16rpx, 24rpx, 32rpx, 48rpx

## 数据模拟

当前版本使用本地数据模拟，包括：
- 用户登录验证（测试账号：13800138000 / 123456）
- 健康数据展示
- 食物信息查询
- 营养计算

## 部署说明

### 微信小程序发布
1. 使用微信开发者工具打开 `dist/build/mp-weixin` 目录
2. 配置小程序 AppID
3. 上传代码并提交审核

### H5部署
1. 构建H5版本：`npm run build:h5`
2. 将 `dist/build/h5` 目录部署到服务器

## 开发注意事项

1. **样式适配**: 使用 rpx 单位确保多端适配
2. **图片资源**: 建议使用 WebP 格式优化加载速度
3. **性能优化**: 合理使用组件懒加载和图片懒加载
4. **兼容性**: 注意微信小程序API的兼容性
5. **安全性**: 敏感数据加密存储，API接口鉴权

## 后续开发计划

- [ ] 接入真实API接口
- [ ] 添加数据统计分析
- [ ] 集成支付功能
- [ ] 增加社区功能
- [ ] 支持多语言
- [ ] 添加离线缓存

## 联系方式

如有问题或建议，请联系开发团队。

## 许可证

MIT License
