{"name": "health-diet-miniprogram", "version": "1.0.0", "description": "慢性病饮食管理微信小程序", "main": "main.js", "scripts": {"dev:mp-weixin": "uni build --watch --platform mp-weixin", "build:mp-weixin": "uni build --platform mp-weixin", "dev:h5": "uni serve", "build:h5": "uni build --platform h5", "dev:app": "uni build --watch --platform app", "build:app": "uni build --platform app", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix"}, "keywords": ["uni-app", "vue3", "微信小程序", "健康管理", "饮食管理", "慢性病"], "author": "Health Diet Team", "license": "MIT", "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "vue": "^3.2.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vite-plugin-uni": "^3.0.0", "@vue/compiler-sfc": "^3.2.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "sass": "^1.56.0", "vite": "^4.0.0"}, "uni-app": {"scripts": {}}, "browserslist": ["Android >= 4.4", "ios >= 9"]}