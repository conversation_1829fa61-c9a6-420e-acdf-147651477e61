<template>
  <view class="register-page">
    <view class="gradient-bg">
      <!-- 返回按钮 -->
      <view class="back-btn" :style="{ paddingTop: statusBarHeight + 'px' }" @tap="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>

      <!-- Logo区域 -->
      <view class="logo-section">
        <view class="logo-container">
          <text class="iconfont icon-heartbeat"></text>
        </view>
        <text class="app-title">创建账号</text>
        <text class="app-subtitle">开始您的健康饮食之旅</text>
      </view>

      <!-- 注册表单 -->
      <view class="register-form">
        <form @submit="handleRegister">
          <!-- 手机号输入 -->
          <view class="input-group">
            <text class="input-label">手机号</text>
            <view class="input-wrapper">
              <text class="iconfont icon-phone input-icon"></text>
              <input 
                type="number" 
                class="form-input"
                placeholder="请输入手机号"
                v-model="registerForm.phone"
                maxlength="11"
              />
            </view>
          </view>

          <!-- 验证码输入 -->
          <view class="input-group">
            <text class="input-label">验证码</text>
            <view class="code-wrapper">
              <view class="input-wrapper">
                <text class="iconfont icon-shield-alt input-icon"></text>
                <input 
                  type="number"
                  class="form-input"
                  placeholder="请输入验证码"
                  v-model="registerForm.code"
                  maxlength="6"
                />
              </view>
              <button 
                class="code-btn"
                :class="{ 'disabled': codeCountdown > 0 }"
                :disabled="codeCountdown > 0 || !canSendCode"
                @tap="sendCode"
              >
                {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
              </button>
            </view>
          </view>

          <!-- 密码输入 -->
          <view class="input-group">
            <text class="input-label">设置密码</text>
            <view class="input-wrapper">
              <text class="iconfont icon-lock input-icon"></text>
              <input 
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                placeholder="请设置密码（6-20位）"
                v-model="registerForm.password"
              />
              <text 
                class="iconfont input-icon-right"
                :class="showPassword ? 'icon-eye-slash' : 'icon-eye'"
                @tap="togglePassword"
              ></text>
            </view>
            <text class="input-hint">密码需包含字母和数字，长度6-20位</text>
          </view>

          <!-- 确认密码 -->
          <view class="input-group">
            <text class="input-label">确认密码</text>
            <view class="input-wrapper">
              <text class="iconfont icon-lock input-icon"></text>
              <input 
                :type="showConfirmPassword ? 'text' : 'password'"
                class="form-input"
                placeholder="请再次输入密码"
                v-model="registerForm.confirmPassword"
              />
              <text 
                class="iconfont input-icon-right"
                :class="showConfirmPassword ? 'icon-eye-slash' : 'icon-eye'"
                @tap="toggleConfirmPassword"
              ></text>
            </view>
          </view>

          <!-- 邀请码（可选） -->
          <view class="input-group">
            <text class="input-label">邀请码（可选）</text>
            <view class="input-wrapper">
              <text class="iconfont icon-gift input-icon"></text>
              <input 
                type="text"
                class="form-input"
                placeholder="输入邀请码可获得额外福利"
                v-model="registerForm.inviteCode"
              />
            </view>
          </view>

          <!-- 协议同意 -->
          <view class="agreement-section">
            <label class="checkbox-wrapper">
              <checkbox 
                :checked="agreeTerms" 
                @change="handleAgreementChange"
                color="#667eea"
              />
              <text class="agreement-text">
                我已阅读并同意
                <text class="agreement-link" @tap="viewUserAgreement">《用户协议》</text>
                和
                <text class="agreement-link" @tap="viewPrivacyPolicy">《隐私政策》</text>
              </text>
            </label>
          </view>

          <!-- 注册按钮 -->
          <button 
            class="register-btn"
            :class="{ 'btn-disabled': !canRegister }"
            :disabled="!canRegister"
            @tap="handleRegister"
          >
            {{ isLoading ? '注册中...' : '立即注册' }}
          </button>
        </form>

        <!-- 登录链接 -->
        <view class="login-link">
          <text class="link-text">已有账号？</text>
          <text class="link-action" @tap="goToLogin">立即登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Register',
  data() {
    return {
      statusBarHeight: 20,
      registerForm: {
        phone: '',
        code: '',
        password: '',
        confirmPassword: '',
        inviteCode: ''
      },
      showPassword: false,
      showConfirmPassword: false,
      agreeTerms: false,
      isLoading: false,
      codeCountdown: 0,
      countdownTimer: null
    }
  },
  computed: {
    canSendCode() {
      return /^1[3-9]\d{9}$/.test(this.registerForm.phone)
    },
    
    canRegister() {
      return this.registerForm.phone.length === 11 && 
             this.registerForm.code.length === 6 &&
             this.registerForm.password.length >= 6 &&
             this.registerForm.confirmPassword === this.registerForm.password &&
             this.agreeTerms &&
             !this.isLoading
    }
  },
  onLoad() {
    this.initPage()
  },
  onUnload() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  },
  methods: {
    initPage() {
      this.statusBarHeight = uni.getStorageSync('statusBarHeight') || 20
    },
    
    goBack() {
      uni.navigateBack()
    },
    
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },
    
    handleAgreementChange(e) {
      this.agreeTerms = e.detail.value.length > 0
    },
    
    async sendCode() {
      if (!this.canSendCode) return
      
      try {
        // 模拟发送验证码
        await this.sendCodeRequest()
        
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
        
        // 开始倒计时
        this.startCountdown()
        
      } catch (error) {
        uni.showToast({
          title: error.message || '发送失败',
          icon: 'none'
        })
      }
    },
    
    sendCodeRequest() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve()
        }, 1000)
      })
    },
    
    startCountdown() {
      this.codeCountdown = 60
      this.countdownTimer = setInterval(() => {
        this.codeCountdown--
        if (this.codeCountdown <= 0) {
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }, 1000)
    },
    
    async handleRegister() {
      if (!this.canRegister) return
      
      // 表单验证
      if (!this.validateForm()) return
      
      this.isLoading = true
      
      try {
        // 模拟注册请求
        await this.registerRequest()
        
        uni.showToast({
          title: '注册成功',
          icon: 'success'
        })
        
        // 跳转到健康档案设置页面
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/health-profile/health-profile'
          })
        }, 1500)
        
      } catch (error) {
        uni.showToast({
          title: error.message || '注册失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },
    
    validateForm() {
      if (!/^1[3-9]\d{9}$/.test(this.registerForm.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }
      
      if (this.registerForm.code.length !== 6) {
        uni.showToast({
          title: '请输入6位验证码',
          icon: 'none'
        })
        return false
      }
      
      if (this.registerForm.password.length < 6) {
        uni.showToast({
          title: '密码长度不能少于6位',
          icon: 'none'
        })
        return false
      }
      
      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        })
        return false
      }
      
      if (!this.agreeTerms) {
        uni.showToast({
          title: '请同意用户协议和隐私政策',
          icon: 'none'
        })
        return false
      }
      
      return true
    },
    
    registerRequest() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve()
        }, 2000)
      })
    },
    
    goToLogin() {
      uni.navigateBack()
    },
    
    viewUserAgreement() {
      uni.showToast({
        title: '用户协议',
        icon: 'none'
      })
    },
    
    viewPrivacyPolicy() {
      uni.showToast({
        title: '隐私政策',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.register-page {
  min-height: 100vh;
}

.gradient-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32rpx 48rpx 64rpx;
}

.back-btn {
  margin-bottom: 32rpx;
  
  .iconfont {
    font-size: 40rpx;
    color: white;
  }
}

.logo-section {
  text-align: center;
  margin-bottom: 48rpx;
}

.logo-container {
  width: 120rpx;
  height: 120rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
  
  .iconfont {
    font-size: 60rpx;
    color: #667eea;
  }
}

.app-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 8rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

.register-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 48rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  display: block;
  margin-bottom: 16rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 24rpx;
  font-size: 32rpx;
  color: #9ca3af;
  z-index: 1;
}

.input-icon-right {
  position: absolute;
  right: 24rpx;
  font-size: 32rpx;
  color: #9ca3af;
  z-index: 1;
}

.form-input {
  width: 100%;
  padding: 24rpx 24rpx 24rpx 72rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: white;
  font-size: 32rpx;
  color: #1f2937;
  
  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  }
}

.input-hint {
  font-size: 24rpx;
  color: #9ca3af;
  margin-top: 8rpx;
  display: block;
}

.code-wrapper {
  display: flex;
  gap: 24rpx;
  
  .input-wrapper {
    flex: 1;
  }
}

.code-btn {
  padding: 24rpx 32rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  white-space: nowrap;
  
  &.disabled {
    background: #d1d5db;
    color: #9ca3af;
  }
}

.agreement-section {
  margin-bottom: 48rpx;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.agreement-text {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.6;
}

.agreement-link {
  color: #667eea;
  text-decoration: underline;
}

.register-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 16rpx 32rpx rgba(102, 126, 234, 0.3);
  
  &.btn-disabled {
    background: #d1d5db;
    box-shadow: none;
  }
}

.login-link {
  text-align: center;
  margin-top: 48rpx;
}

.link-text {
  font-size: 28rpx;
  color: #6b7280;
}

.link-action {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
  margin-left: 8rpx;
}
</style>
