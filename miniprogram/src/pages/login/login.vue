<template>
  <view class="login-page">
    <!-- 背景渐变 -->
    <view class="gradient-bg">
      <!-- Logo区域 -->
      <view class="logo-section">
        <view class="logo-container">
          <text class="iconfont icon-heartbeat"></text>
        </view>
        <text class="app-title">健康饮食</text>
        <text class="app-subtitle">专业的慢性病饮食管理助手</text>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <text class="form-title">欢迎回来</text>
        
        <form @submit="handleLogin">
          <!-- 手机号输入 -->
          <view class="input-group">
            <text class="input-label">手机号</text>
            <view class="input-wrapper">
              <text class="iconfont icon-phone input-icon"></text>
              <input 
                type="number" 
                class="form-input"
                placeholder="请输入手机号"
                v-model="loginForm.phone"
                maxlength="11"
              />
            </view>
          </view>

          <!-- 密码输入 -->
          <view class="input-group">
            <text class="input-label">密码</text>
            <view class="input-wrapper">
              <text class="iconfont icon-lock input-icon"></text>
              <input 
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                placeholder="请输入密码"
                v-model="loginForm.password"
              />
              <text 
                class="iconfont input-icon-right"
                :class="showPassword ? 'icon-eye-slash' : 'icon-eye'"
                @tap="togglePassword"
              ></text>
            </view>
          </view>

          <!-- 记住密码和忘记密码 -->
          <view class="form-options">
            <label class="checkbox-wrapper">
              <checkbox 
                :checked="rememberPassword" 
                @change="handleRememberChange"
                color="#667eea"
              />
              <text class="checkbox-label">记住密码</text>
            </label>
            <text class="forgot-password" @tap="handleForgotPassword">忘记密码？</text>
          </view>

          <!-- 登录按钮 -->
          <button 
            class="login-btn"
            :class="{ 'btn-disabled': !canLogin }"
            :disabled="!canLogin"
            @tap="handleLogin"
          >
            {{ isLoading ? '登录中...' : '登录' }}
          </button>
        </form>

        <!-- 第三方登录 -->
        <view class="third-party-login">
          <view class="divider">
            <text class="divider-text">或使用以下方式登录</text>
          </view>

          <view class="third-party-buttons">
            <button class="third-party-btn" @tap="handleWechatLogin">
              <text class="iconfont icon-wechat"></text>
              <text class="btn-text">微信</text>
            </button>
            <button class="third-party-btn" @tap="handleAppleLogin">
              <text class="iconfont icon-apple"></text>
              <text class="btn-text">Apple ID</text>
            </button>
          </view>
        </view>

        <!-- 注册链接 -->
        <view class="register-link">
          <text class="link-text">还没有账号？</text>
          <text class="link-action" @tap="goToRegister">立即注册</text>
        </view>
      </view>

      <!-- 隐私协议 -->
      <view class="privacy-agreement">
        <text class="agreement-text">
          登录即表示您同意我们的
          <text class="agreement-link" @tap="viewUserAgreement">用户协议</text>
          和
          <text class="agreement-link" @tap="viewPrivacyPolicy">隐私政策</text>
        </text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        phone: '',
        password: ''
      },
      showPassword: false,
      rememberPassword: false,
      isLoading: false
    }
  },
  computed: {
    canLogin() {
      return this.loginForm.phone.length === 11 && 
             this.loginForm.password.length >= 6 && 
             !this.isLoading
    }
  },
  onLoad() {
    this.loadSavedCredentials()
  },
  methods: {
    loadSavedCredentials() {
      // 加载保存的登录信息
      const savedPhone = uni.getStorageSync('savedPhone')
      const savedPassword = uni.getStorageSync('savedPassword')
      const rememberPassword = uni.getStorageSync('rememberPassword')
      
      if (rememberPassword) {
        this.loginForm.phone = savedPhone || ''
        this.loginForm.password = savedPassword || ''
        this.rememberPassword = true
      }
    },
    
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    handleRememberChange(e) {
      this.rememberPassword = e.detail.value.length > 0
    },
    
    async handleLogin() {
      if (!this.canLogin) return
      
      // 表单验证
      if (!this.validateForm()) return
      
      this.isLoading = true
      
      try {
        // 模拟登录请求
        await this.loginRequest()
        
        // 保存登录信息
        if (this.rememberPassword) {
          uni.setStorageSync('savedPhone', this.loginForm.phone)
          uni.setStorageSync('savedPassword', this.loginForm.password)
          uni.setStorageSync('rememberPassword', true)
        } else {
          uni.removeStorageSync('savedPhone')
          uni.removeStorageSync('savedPassword')
          uni.removeStorageSync('rememberPassword')
        }
        
        // 保存登录状态
        uni.setStorageSync('token', 'mock_token_' + Date.now())
        uni.setStorageSync('userData', {
          name: '张先生',
          phone: this.loginForm.phone,
          avatar: '/static/images/default-avatar.png'
        })
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index'
          })
        }, 1500)
        
      } catch (error) {
        uni.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },
    
    validateForm() {
      if (!/^1[3-9]\d{9}$/.test(this.loginForm.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }
      
      if (this.loginForm.password.length < 6) {
        uni.showToast({
          title: '密码长度不能少于6位',
          icon: 'none'
        })
        return false
      }
      
      return true
    },
    
    loginRequest() {
      // 模拟登录请求
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (this.loginForm.phone === '13800138000' && this.loginForm.password === '123456') {
            resolve()
          } else {
            reject(new Error('手机号或密码错误'))
          }
        }, 2000)
      })
    },
    
    handleForgotPassword() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    handleWechatLogin() {
      uni.showToast({
        title: '微信登录开发中',
        icon: 'none'
      })
    },
    
    handleAppleLogin() {
      uni.showToast({
        title: 'Apple登录开发中',
        icon: 'none'
      })
    },
    
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    },
    
    viewUserAgreement() {
      uni.showToast({
        title: '用户协议',
        icon: 'none'
      })
    },
    
    viewPrivacyPolicy() {
      uni.showToast({
        title: '隐私政策',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
}

.gradient-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 64rpx 48rpx;
}

.logo-section {
  text-align: center;
  margin-bottom: 64rpx;
}

.logo-container {
  width: 160rpx;
  height: 160rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);

  .iconfont {
    font-size: 80rpx;
    color: #667eea;
  }
}

.app-title {
  font-size: 60rpx;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 16rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 48rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
  text-align: center;
  display: block;
  margin-bottom: 48rpx;
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  display: block;
  margin-bottom: 16rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 24rpx;
  font-size: 32rpx;
  color: #9ca3af;
  z-index: 1;
}

.input-icon-right {
  position: absolute;
  right: 24rpx;
  font-size: 32rpx;
  color: #9ca3af;
  z-index: 1;
}

.form-input {
  width: 100%;
  padding: 24rpx 24rpx 24rpx 72rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: white;
  font-size: 32rpx;
  color: #1f2937;

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 48rpx;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
}

.checkbox-label {
  font-size: 28rpx;
  color: #6b7280;
  margin-left: 16rpx;
}

.forgot-password {
  font-size: 28rpx;
  color: #667eea;
}

.login-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 16rpx 32rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 8rpx 16rpx rgba(102, 126, 234, 0.3);
  }

  &.btn-disabled {
    background: #d1d5db;
    box-shadow: none;
    transform: none;
  }
}

.third-party-login {
  margin-top: 48rpx;
}

.divider {
  position: relative;
  text-align: center;
  margin-bottom: 32rpx;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1rpx;
    background: #e5e7eb;
  }
}

.divider-text {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 32rpx;
  font-size: 28rpx;
  color: #9ca3af;
}

.third-party-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.third-party-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: white;

  .iconfont {
    font-size: 32rpx;
    margin-right: 16rpx;

    &.icon-wechat {
      color: #07c160;
    }

    &.icon-apple {
      color: #000000;
    }
  }
}

.btn-text {
  font-size: 28rpx;
  color: #374151;
}

.register-link {
  text-align: center;
  margin-top: 48rpx;
}

.link-text {
  font-size: 28rpx;
  color: #6b7280;
}

.link-action {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
  margin-left: 8rpx;
}

.privacy-agreement {
  text-align: center;
  margin-top: 48rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.agreement-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: underline;
}
</style>
