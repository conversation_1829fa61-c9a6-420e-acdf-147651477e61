<template>
  <view class="profile-page">
    <!-- 用户信息头部 -->
    <view class="user-header gradient-primary" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="user-info">
          <view class="user-avatar-section">
            <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill"></image>
            <view class="user-details">
              <text class="user-name">{{ userInfo.name }}</text>
              <text class="user-desc">{{ userInfo.condition }} · {{ userInfo.age }}岁</text>
              <view class="vip-badge">
                <text class="vip-text">VIP会员</text>
              </view>
            </view>
          </view>
          <view class="settings-btn" @tap="goToSettings">
            <text class="iconfont icon-setting"></text>
          </view>
        </view>
        
        <!-- 健康数据概览 -->
        <view class="health-overview">
          <view class="overview-item" v-for="(item, index) in healthOverview" :key="index">
            <text class="overview-value">{{ item.value }}</text>
            <text class="overview-label">{{ item.label }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content" :style="{ paddingTop: headerHeight + 'px' }">
      <!-- VIP会员卡片 -->
      <view class="vip-card gradient-gold">
        <view class="vip-content">
          <view class="vip-info">
            <text class="vip-title">VIP会员</text>
            <text class="vip-subtitle">专享个性化饮食方案</text>
            <text class="vip-expire">有效期至：{{ vipExpireDate }}</text>
          </view>
          <view class="vip-icon">
            <text class="iconfont icon-crown"></text>
          </view>
        </view>
        <button class="renew-btn" @tap="renewVip">
          续费会员
        </button>
      </view>

      <!-- 功能菜单 -->
      <view class="menu-section">
        <view class="section-title">我的功能</view>
        <view class="menu-card">
          <view 
            class="menu-item"
            v-for="(item, index) in functionMenus"
            :key="index"
            @tap="handleMenuClick(item)"
          >
            <view class="menu-icon" :class="item.iconBg">
              <text class="iconfont" :class="item.icon"></text>
            </view>
            <view class="menu-content">
              <text class="menu-title">{{ item.title }}</text>
              <text class="menu-desc">{{ item.desc }}</text>
            </view>
            <text class="iconfont icon-chevron-right menu-arrow"></text>
          </view>
        </view>
      </view>

      <!-- 数据管理 -->
      <view class="menu-section">
        <view class="section-title">数据管理</view>
        <view class="menu-card">
          <view 
            class="menu-item"
            v-for="(item, index) in dataMenus"
            :key="index"
            @tap="handleMenuClick(item)"
          >
            <view class="menu-icon" :class="item.iconBg">
              <text class="iconfont" :class="item.icon"></text>
            </view>
            <view class="menu-content">
              <text class="menu-title">{{ item.title }}</text>
              <text class="menu-desc">{{ item.desc }}</text>
            </view>
            <view class="menu-extra" v-if="item.extra">
              <text class="extra-text" :class="item.extraClass">{{ item.extra }}</text>
              <text class="iconfont icon-chevron-right menu-arrow"></text>
            </view>
            <text class="iconfont icon-chevron-right menu-arrow" v-else></text>
          </view>
        </view>
      </view>

      <!-- 帮助与支持 -->
      <view class="menu-section">
        <view class="section-title">帮助与支持</view>
        <view class="menu-card">
          <view 
            class="menu-item"
            v-for="(item, index) in helpMenus"
            :key="index"
            @tap="handleMenuClick(item)"
          >
            <view class="menu-icon" :class="item.iconBg">
              <text class="iconfont" :class="item.icon"></text>
            </view>
            <text class="menu-title">{{ item.title }}</text>
            <text class="iconfont icon-chevron-right menu-arrow"></text>
          </view>
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="logout-section">
        <button class="logout-btn" @tap="handleLogout">
          退出登录
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Profile',
  data() {
    return {
      statusBarHeight: 20,
      headerHeight: 300,
      userInfo: {
        name: '张先生',
        condition: '糖尿病患者',
        age: 35,
        avatar: '/static/images/default-avatar.png'
      },
      vipExpireDate: '2024-12-31',
      healthOverview: [
        { label: '管理天数', value: '32' },
        { label: '健康评分', value: '85' },
        { label: '体重变化(kg)', value: '-2.3' }
      ],
      functionMenus: [
        {
          title: '个人资料',
          desc: '编辑基本信息和健康档案',
          icon: 'icon-user-edit',
          iconBg: 'bg-blue',
          action: 'profile'
        },
        {
          title: '健康报告',
          desc: '查看详细的健康分析报告',
          icon: 'icon-chart-line',
          iconBg: 'bg-green',
          action: 'report'
        },
        {
          title: '饮食偏好',
          desc: '设置口味偏好和忌口食物',
          icon: 'icon-utensils',
          iconBg: 'bg-purple',
          action: 'preference'
        },
        {
          title: '提醒设置',
          desc: '用药提醒、饮食提醒等',
          icon: 'icon-bell',
          iconBg: 'bg-red',
          action: 'reminder'
        }
      ],
      dataMenus: [
        {
          title: '数据同步',
          desc: '同步到云端，多设备访问',
          icon: 'icon-cloud',
          iconBg: 'bg-blue',
          extra: '已同步',
          extraClass: 'text-success',
          action: 'sync'
        },
        {
          title: '数据导出',
          desc: '导出健康数据和饮食记录',
          icon: 'icon-download',
          iconBg: 'bg-orange',
          action: 'export'
        },
        {
          title: '隐私设置',
          desc: '管理数据隐私和权限',
          icon: 'icon-shield',
          iconBg: 'bg-gray',
          action: 'privacy'
        }
      ],
      helpMenus: [
        {
          title: '常见问题',
          icon: 'icon-question',
          iconBg: 'bg-green',
          action: 'faq'
        },
        {
          title: '联系客服',
          icon: 'icon-headset',
          iconBg: 'bg-blue',
          action: 'contact'
        },
        {
          title: '评价应用',
          icon: 'icon-star',
          iconBg: 'bg-purple',
          action: 'rate'
        },
        {
          title: '关于我们',
          icon: 'icon-info',
          iconBg: 'bg-gray',
          action: 'about'
        }
      ]
    }
  },
  onLoad() {
    this.initPage()
  },
  onShow() {
    this.loadUserData()
  },
  methods: {
    initPage() {
      this.statusBarHeight = uni.getStorageSync('statusBarHeight') || 20
    },
    
    loadUserData() {
      const userData = uni.getStorageSync('userData')
      if (userData) {
        this.userInfo = { ...this.userInfo, ...userData }
      }
    },
    
    goToSettings() {
      uni.showToast({
        title: '设置功能开发中',
        icon: 'none'
      })
    },
    
    renewVip() {
      uni.navigateTo({
        url: '/pages/vip-subscription/vip-subscription'
      })
    },
    
    handleMenuClick(item) {
      switch (item.action) {
        case 'profile':
          uni.navigateTo({
            url: '/pages/health-profile/health-profile'
          })
          break
        case 'report':
          uni.switchTab({
            url: '/pages/health-management/health-management'
          })
          break
        default:
          uni.showToast({
            title: `${item.title}功能开发中`,
            icon: 'none'
          })
      }
    },
    
    handleLogout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除登录状态
            uni.removeStorageSync('token')
            uni.removeStorageSync('userData')
            
            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })
            
            // 跳转到登录页
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/login/login'
              })
            }, 1500)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.user-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  color: white;
}

.header-content {
  padding: 32rpx;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.user-avatar-section {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 8rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 24rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
  margin-bottom: 16rpx;
}

.vip-badge {
  display: inline-block;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
}

.vip-text {
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.settings-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .iconfont {
    font-size: 36rpx;
  }
}

.health-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  text-align: center;
}

.overview-item {
  .overview-value {
    font-size: 48rpx;
    font-weight: bold;
    display: block;
    margin-bottom: 8rpx;
  }

  .overview-label {
    font-size: 24rpx;
    opacity: 0.8;
    display: block;
  }
}

.page-content {
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.vip-card {
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  color: white;
  box-shadow: 0 16rpx 48rpx rgba(251, 191, 36, 0.3);
}

.vip-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.vip-info {
  flex: 1;
}

.vip-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.vip-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  margin-bottom: 8rpx;
}

.vip-expire {
  font-size: 24rpx;
  opacity: 0.8;
  display: block;
}

.vip-icon {
  .iconfont {
    font-size: 80rpx;
    opacity: 0.8;
  }
}

.renew-btn {
  width: 100%;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.menu-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

.menu-card {
  background: white;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f3f4f6;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8fafc;
    transform: translateX(8rpx);
  }

  &:last-child {
    border-bottom: none;
  }
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;

  .iconfont {
    font-size: 36rpx;
  }

  &.bg-blue {
    background: #dbeafe;

    .iconfont {
      color: #3b82f6;
    }
  }

  &.bg-green {
    background: #dcfce7;

    .iconfont {
      color: #22c55e;
    }
  }

  &.bg-purple {
    background: #ede9fe;

    .iconfont {
      color: #8b5cf6;
    }
  }

  &.bg-red {
    background: #fee2e2;

    .iconfont {
      color: #ef4444;
    }
  }

  &.bg-orange {
    background: #fed7aa;

    .iconfont {
      color: #f97316;
    }
  }

  &.bg-gray {
    background: #f3f4f6;

    .iconfont {
      color: #6b7280;
    }
  }
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}

.menu-desc {
  font-size: 28rpx;
  color: #9ca3af;
  display: block;
}

.menu-extra {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.extra-text {
  font-size: 28rpx;

  &.text-success {
    color: #22c55e;
  }
}

.menu-arrow {
  font-size: 24rpx;
  color: #9ca3af;
}

.logout-section {
  margin-top: 48rpx;
}

.logout-btn {
  width: 100%;
  padding: 32rpx;
  background: white;
  color: #ef4444;
  border: none;
  border-radius: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}
</style>
