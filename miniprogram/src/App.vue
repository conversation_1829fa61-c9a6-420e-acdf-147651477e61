<template>
  <view id="app">
    <!-- 应用入口 -->
  </view>
</template>

<script>
export default {
  name: 'App',
  onLaunch: function() {
    console.log('App Launch')
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 获取系统信息
    this.getSystemInfo()
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      const token = uni.getStorageSync('token')
      if (!token) {
        // 未登录，跳转到登录页
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    },
    
    // 获取系统信息
    getSystemInfo() {
      uni.getSystemInfo({
        success: (res) => {
          // 存储系统信息
          uni.setStorageSync('systemInfo', res)
          
          // 设置状态栏高度
          const statusBarHeight = res.statusBarHeight || 20
          uni.setStorageSync('statusBarHeight', statusBarHeight)
          
          // 设置导航栏高度
          const navBarHeight = statusBarHeight + 44
          uni.setStorageSync('navBarHeight', navBarHeight)
        }
      })
    }
  }
}
</script>

<style>
/* 应用基础样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #1f2937;
}

/* 渐变背景 */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.gradient-gold {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

/* 字体图标基础样式 */
.iconfont {
  font-family: "iconfont" !important;
  font-size: 32rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
