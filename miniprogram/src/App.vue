<template>
  <view id="app">
    <!-- 应用入口 -->
  </view>
</template>

<script>
export default {
  name: 'App',
  onLaunch: function() {
    console.log('App Launch')
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 获取系统信息
    this.getSystemInfo()
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      const token = uni.getStorageSync('token')
      if (!token) {
        // 未登录，跳转到登录页
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    },
    
    // 获取系统信息
    getSystemInfo() {
      uni.getSystemInfo({
        success: (res) => {
          // 存储系统信息
          uni.setStorageSync('systemInfo', res)
          
          // 设置状态栏高度
          const statusBarHeight = res.statusBarHeight || 20
          uni.setStorageSync('statusBarHeight', statusBarHeight)
          
          // 设置导航栏高度
          const navBarHeight = statusBarHeight + 44
          uni.setStorageSync('navBarHeight', navBarHeight)
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 全局样式 */
@import url('./static/styles/global.scss');

/* 字体图标 */
@import url('./static/fonts/iconfont.css');

/* 应用基础样式 */
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 页面基础样式 */
page {
  background-color: #f5f5f5;
  font-size: 28rpx;
  line-height: 1.6;
}

/* 通用样式类 */
.container {
  padding: 0 32rpx;
}

.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 渐变背景 */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.gradient-gold {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-padding {
  padding: 32rpx;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

/* 文本样式 */
.text-primary {
  color: #667eea;
}

.text-success {
  color: #22c55e;
}

.text-warning {
  color: #f59e0b;
}

.text-danger {
  color: #ef4444;
}

.text-gray {
  color: #6b7280;
}

.text-gray-light {
  color: #9ca3af;
}

/* 间距工具类 */
.mt-16 { margin-top: 32rpx; }
.mt-24 { margin-top: 48rpx; }
.mt-32 { margin-top: 64rpx; }

.mb-16 { margin-bottom: 32rpx; }
.mb-24 { margin-bottom: 48rpx; }
.mb-32 { margin-bottom: 64rpx; }

.p-16 { padding: 32rpx; }
.p-24 { padding: 48rpx; }
.p-32 { padding: 64rpx; }

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  justify-content: space-around;
  align-items: center;
}

/* 圆角工具类 */
.rounded-sm { border-radius: 8rpx; }
.rounded { border-radius: 16rpx; }
.rounded-lg { border-radius: 24rpx; }
.rounded-xl { border-radius: 32rpx; }
.rounded-full { border-radius: 50%; }

/* 阴影工具类 */
.shadow-sm {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.shadow {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.shadow-lg {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
</style>
