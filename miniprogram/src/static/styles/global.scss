/* 全局样式文件 */

/* 重置样式 */
view, text, image, button, input, textarea {
  box-sizing: border-box;
}

/* 页面基础样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #1f2937;
}

/* 容器样式 */
.container {
  padding: 0 32rpx;
}

.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 渐变背景类 */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.gradient-gold {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.gradient-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-padding {
  padding: 32rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-success {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(34, 197, 94, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(245, 158, 11, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(239, 68, 68, 0.3);
}

.btn-disabled {
  background: #d1d5db !important;
  color: #9ca3af !important;
  box-shadow: none !important;
  transform: none !important;
  cursor: not-allowed;
}

/* 文本样式 */
.text-primary {
  color: #667eea;
}

.text-success {
  color: #22c55e;
}

.text-warning {
  color: #f59e0b;
}

.text-danger {
  color: #ef4444;
}

.text-gray {
  color: #6b7280;
}

.text-gray-light {
  color: #9ca3af;
}

.text-gray-dark {
  color: #374151;
}

.text-black {
  color: #1f2937;
}

.text-white {
  color: #ffffff;
}

/* 字体大小 */
.text-xs {
  font-size: 24rpx;
}

.text-sm {
  font-size: 28rpx;
}

.text-base {
  font-size: 32rpx;
}

.text-lg {
  font-size: 36rpx;
}

.text-xl {
  font-size: 40rpx;
}

.text-2xl {
  font-size: 48rpx;
}

.text-3xl {
  font-size: 60rpx;
}

/* 字体粗细 */
.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }
.m-5 { margin: 40rpx; }
.m-6 { margin: 48rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-3 { margin-top: 24rpx; }
.mt-4 { margin-top: 32rpx; }
.mt-5 { margin-top: 40rpx; }
.mt-6 { margin-top: 48rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-3 { margin-bottom: 24rpx; }
.mb-4 { margin-bottom: 32rpx; }
.mb-5 { margin-bottom: 40rpx; }
.mb-6 { margin-bottom: 48rpx; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 8rpx; }
.ml-2 { margin-left: 16rpx; }
.ml-3 { margin-left: 24rpx; }
.ml-4 { margin-left: 32rpx; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 8rpx; }
.mr-2 { margin-right: 16rpx; }
.mr-3 { margin-right: 24rpx; }
.mr-4 { margin-right: 32rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }
.p-5 { padding: 40rpx; }
.p-6 { padding: 48rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 8rpx; }
.pt-2 { padding-top: 16rpx; }
.pt-3 { padding-top: 24rpx; }
.pt-4 { padding-top: 32rpx; }
.pt-5 { padding-top: 40rpx; }
.pt-6 { padding-top: 48rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 8rpx; }
.pb-2 { padding-bottom: 16rpx; }
.pb-3 { padding-bottom: 24rpx; }
.pb-4 { padding-bottom: 32rpx; }
.pb-5 { padding-bottom: 40rpx; }
.pb-6 { padding-bottom: 48rpx; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 8rpx; }
.pl-2 { padding-left: 16rpx; }
.pl-3 { padding-left: 24rpx; }
.pl-4 { padding-left: 32rpx; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 8rpx; }
.pr-2 { padding-right: 16rpx; }
.pr-3 { padding-right: 24rpx; }
.pr-4 { padding-right: 32rpx; }

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  justify-content: space-around;
  align-items: center;
}

.flex-start {
  justify-content: flex-start;
  align-items: center;
}

.flex-end {
  justify-content: flex-end;
  align-items: center;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

/* 圆角工具类 */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: 8rpx; }
.rounded { border-radius: 16rpx; }
.rounded-md { border-radius: 20rpx; }
.rounded-lg { border-radius: 24rpx; }
.rounded-xl { border-radius: 32rpx; }
.rounded-2xl { border-radius: 40rpx; }
.rounded-full { border-radius: 50%; }

/* 阴影工具类 */
.shadow-none {
  box-shadow: none;
}

.shadow-sm {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.shadow {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.shadow-md {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.10);
}

.shadow-lg {
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.12);
}

.shadow-xl {
  box-shadow: 0 24rpx 48rpx rgba(0, 0, 0, 0.15);
}

/* 宽度高度 */
.w-full { width: 100%; }
.h-full { height: 100%; }

/* 定位 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

/* 显示隐藏 */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* 溢出处理 */
.overflow-hidden { overflow: hidden; }
.overflow-scroll { overflow: scroll; }

/* 透明度 */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }
