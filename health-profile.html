<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康档案设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .input-focus:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .step-indicator {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .step-inactive {
            background: #e5e7eb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <div class="bg-white shadow-sm">
        <div class="flex items-center justify-between px-4 py-3">
            <button class="text-gray-600">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-800">完善健康档案</h1>
            <span class="text-sm text-purple-600">跳过</span>
        </div>
    </div>

    <!-- 进度指示器 -->
    <div class="bg-white px-4 py-4">
        <div class="flex items-center justify-between mb-2">
            <span class="text-sm text-gray-600">第1步，共3步</span>
            <span class="text-sm text-purple-600">33%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="step-indicator h-2 rounded-full" style="width: 33%"></div>
        </div>
    </div>

    <div class="px-4 py-6 space-y-6">
        <!-- 基本信息卡片 -->
        <div class="bg-white rounded-xl p-6 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-user-circle text-purple-600 mr-2"></i>
                基本信息
            </h2>
            
            <div class="space-y-4">
                <!-- 性别选择 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">性别</label>
                    <div class="grid grid-cols-2 gap-3">
                        <button class="flex items-center justify-center p-3 border-2 border-purple-500 bg-purple-50 rounded-lg">
                            <i class="fas fa-mars text-blue-500 mr-2"></i>
                            <span class="font-medium text-purple-700">男</span>
                        </button>
                        <button class="flex items-center justify-center p-3 border-2 border-gray-200 bg-white rounded-lg">
                            <i class="fas fa-venus text-pink-500 mr-2"></i>
                            <span class="text-gray-600">女</span>
                        </button>
                    </div>
                </div>

                <!-- 年龄输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">年龄</label>
                    <div class="relative">
                        <input type="number" 
                               class="input-focus block w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900"
                               placeholder="请输入您的年龄"
                               value="35">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 text-sm">岁</span>
                        </div>
                    </div>
                </div>

                <!-- 身高输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">身高</label>
                    <div class="relative">
                        <input type="number" 
                               class="input-focus block w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900"
                               placeholder="请输入您的身高"
                               value="170">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 text-sm">cm</span>
                        </div>
                    </div>
                </div>

                <!-- 体重输入 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">当前体重</label>
                    <div class="relative">
                        <input type="number" 
                               class="input-focus block w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900"
                               placeholder="请输入您的体重"
                               value="68.5">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 text-sm">kg</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 慢性病信息卡片 -->
        <div class="bg-white rounded-xl p-6 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-stethoscope text-red-500 mr-2"></i>
                慢性病情况
            </h2>
            
            <div class="space-y-3">
                <p class="text-sm text-gray-600 mb-4">请选择您目前患有的慢性病（可多选）</p>
                
                <div class="grid grid-cols-2 gap-3">
                    <label class="flex items-center p-3 border-2 border-red-500 bg-red-50 rounded-lg cursor-pointer">
                        <input type="checkbox" class="sr-only" checked>
                        <i class="fas fa-check-circle text-red-500 mr-2"></i>
                        <span class="text-red-700 font-medium">糖尿病</span>
                    </label>
                    
                    <label class="flex items-center p-3 border-2 border-gray-200 bg-white rounded-lg cursor-pointer">
                        <input type="checkbox" class="sr-only">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span class="text-gray-600">高血压</span>
                    </label>
                    
                    <label class="flex items-center p-3 border-2 border-gray-200 bg-white rounded-lg cursor-pointer">
                        <input type="checkbox" class="sr-only">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span class="text-gray-600">高血脂</span>
                    </label>
                    
                    <label class="flex items-center p-3 border-2 border-gray-200 bg-white rounded-lg cursor-pointer">
                        <input type="checkbox" class="sr-only">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span class="text-gray-600">心脏病</span>
                    </label>
                    
                    <label class="flex items-center p-3 border-2 border-gray-200 bg-white rounded-lg cursor-pointer">
                        <input type="checkbox" class="sr-only">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span class="text-gray-600">肾病</span>
                    </label>
                    
                    <label class="flex items-center p-3 border-2 border-gray-200 bg-white rounded-lg cursor-pointer">
                        <input type="checkbox" class="sr-only">
                        <i class="far fa-circle text-gray-400 mr-2"></i>
                        <span class="text-gray-600">其他</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- 过敏信息卡片 -->
        <div class="bg-white rounded-xl p-6 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                过敏信息
            </h2>
            
            <div class="space-y-4">
                <p class="text-sm text-gray-600">请告诉我们您的食物过敏情况</p>
                
                <textarea class="input-focus w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-gray-900 resize-none"
                          rows="3"
                          placeholder="例如：对海鲜过敏、对花生过敏等..."></textarea>
                
                <div class="flex flex-wrap gap-2">
                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">海鲜</span>
                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">花生</span>
                    <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">牛奶</span>
                    <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">鸡蛋</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <button class="btn-primary w-full py-3 px-4 rounded-lg text-white font-semibold">
            下一步
        </button>
    </div>
</body>
</html>
