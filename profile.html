<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .gradient-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .tab-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        .menu-item {
            transition: all 0.3s ease;
        }
        .menu-item:hover {
            background-color: #f8fafc;
            transform: translateX(4px);
        }
        .vip-badge {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        }
    </style>
</head>
<body class="bg-gray-50 pb-20">
    <!-- 用户信息头部 -->
    <div class="gradient-header text-white">
        <div class="px-4 py-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face" 
                         class="w-16 h-16 rounded-full border-4 border-white border-opacity-30 mr-4">
                    <div>
                        <h1 class="text-xl font-bold">张先生</h1>
                        <p class="text-purple-100 text-sm">糖尿病患者 · 35岁</p>
                        <div class="flex items-center mt-1">
                            <div class="vip-badge px-2 py-1 rounded-full">
                                <span class="text-xs font-bold text-white">VIP会员</span>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="text-white">
                    <i class="fas fa-cog text-xl"></i>
                </button>
            </div>
            
            <!-- 健康数据概览 -->
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <p class="text-2xl font-bold">32</p>
                    <p class="text-purple-100 text-xs">管理天数</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold">85</p>
                    <p class="text-purple-100 text-xs">健康评分</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold">-2.3</p>
                    <p class="text-purple-100 text-xs">体重变化(kg)</p>
                </div>
            </div>
        </div>
    </div>

    <div class="px-4 py-6 space-y-6">
        <!-- VIP会员卡片 -->
        <div class="vip-badge rounded-2xl p-6 text-white shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-bold mb-1">VIP会员</h2>
                    <p class="text-yellow-100 text-sm">专享个性化饮食方案</p>
                    <p class="text-yellow-100 text-xs">有效期至：2024-12-31</p>
                </div>
                <div class="text-right">
                    <i class="fas fa-crown text-3xl text-yellow-200"></i>
                </div>
            </div>
            <button class="w-full mt-4 bg-white bg-opacity-20 text-white py-2 rounded-lg text-sm font-medium">
                续费会员
            </button>
        </div>

        <!-- 功能菜单 -->
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-100">
                <h2 class="text-lg font-semibold text-gray-800">我的功能</h2>
            </div>
            
            <div class="divide-y divide-gray-100">
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user-edit text-blue-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">个人资料</p>
                            <p class="text-sm text-gray-500">编辑基本信息和健康档案</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-chart-line text-green-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">健康报告</p>
                            <p class="text-sm text-gray-500">查看详细的健康分析报告</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-utensils text-purple-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">饮食偏好</p>
                            <p class="text-sm text-gray-500">设置口味偏好和忌口食物</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-bell text-red-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">提醒设置</p>
                            <p class="text-sm text-gray-500">用药提醒、饮食提醒等</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 数据管理 -->
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-100">
                <h2 class="text-lg font-semibold text-gray-800">数据管理</h2>
            </div>
            
            <div class="divide-y divide-gray-100">
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-cloud text-blue-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">数据同步</p>
                            <p class="text-sm text-gray-500">同步到云端，多设备访问</p>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm text-green-600 mr-2">已同步</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-download text-orange-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">数据导出</p>
                            <p class="text-sm text-gray-500">导出健康数据和饮食记录</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-shield-alt text-gray-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">隐私设置</p>
                            <p class="text-sm text-gray-500">管理数据隐私和权限</p>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 帮助与支持 -->
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-100">
                <h2 class="text-lg font-semibold text-gray-800">帮助与支持</h2>
            </div>
            
            <div class="divide-y divide-gray-100">
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-question-circle text-green-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">常见问题</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-headset text-blue-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">联系客服</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-star text-purple-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">评价应用</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="menu-item px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-info-circle text-gray-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">关于我们</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 退出登录 -->
        <div class="bg-white rounded-2xl shadow-sm">
            <button class="w-full px-6 py-4 text-red-600 font-medium">
                退出登录
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar fixed bottom-0 left-0 right-0 px-4 py-2">
        <div class="flex items-center justify-around">
            <div class="text-center">
                <i class="fas fa-home text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">首页</p>
            </div>
            <div class="text-center">
                <i class="fas fa-utensils text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">饮食</p>
            </div>
            <div class="text-center">
                <i class="fas fa-search text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">查询</p>
            </div>
            <div class="text-center">
                <i class="fas fa-chart-bar text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">健康</p>
            </div>
            <div class="text-center">
                <i class="fas fa-user text-purple-600 text-xl mb-1"></i>
                <p class="text-xs text-purple-600 font-medium">我的</p>
            </div>
        </div>
    </div>
</body>
</html>
