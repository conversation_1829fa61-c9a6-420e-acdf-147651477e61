<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .gradient-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .health-score { background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%); }
        .warning-card { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .tab-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 pb-20">
    <!-- 顶部问候区域 -->
    <div class="gradient-card text-white p-6 rounded-b-3xl">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h1 class="text-2xl font-bold">早上好，张先生</h1>
                <p class="text-purple-100 text-sm">今天是您健康管理的第32天</p>
            </div>
            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <i class="fas fa-bell text-white text-lg"></i>
            </div>
        </div>
        
        <!-- 今日健康评分 -->
        <div class="health-score rounded-2xl p-4 mt-4">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-white font-semibold mb-1">今日健康评分</h3>
                    <div class="flex items-baseline">
                        <span class="text-3xl font-bold text-white">85</span>
                        <span class="text-white text-sm ml-1">分</span>
                        <span class="text-green-100 text-sm ml-2">↑ 5分</span>
                    </div>
                </div>
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-heart text-white text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="px-4 py-6 space-y-6">
        <!-- 今日提醒 -->
        <div class="warning-card rounded-2xl p-4 text-white">
            <div class="flex items-center mb-2">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span class="font-semibold">重要提醒</span>
            </div>
            <p class="text-sm text-orange-100">您有2个用药提醒和1个体检预约</p>
            <button class="mt-2 bg-white bg-opacity-20 px-3 py-1 rounded-lg text-sm">查看详情</button>
        </div>

        <!-- 今日饮食概览 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-utensils text-green-500 mr-2"></i>
                    今日饮食
                </h2>
                <span class="text-sm text-purple-600">查看详情</span>
            </div>
            
            <div class="grid grid-cols-3 gap-4 mb-4">
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-check text-green-600"></i>
                    </div>
                    <p class="text-xs text-gray-600">早餐</p>
                    <p class="text-sm font-semibold text-gray-800">已记录</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-clock text-yellow-600"></i>
                    </div>
                    <p class="text-xs text-gray-600">午餐</p>
                    <p class="text-sm font-semibold text-gray-800">待记录</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-plus text-gray-400"></i>
                    </div>
                    <p class="text-xs text-gray-600">晚餐</p>
                    <p class="text-sm font-semibold text-gray-800">未开始</p>
                </div>
            </div>
            
            <!-- 营养摄入进度 -->
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">热量</span>
                    <span class="text-sm text-gray-800">680/1800 kcal</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: 38%"></div>
                </div>
                
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">碳水化合物</span>
                    <span class="text-sm text-gray-800">85/225 g</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: 38%"></div>
                </div>
            </div>
        </div>

        <!-- 快捷功能 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">快捷功能</h2>
            <div class="grid grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-search text-blue-600"></i>
                    </div>
                    <p class="text-xs text-gray-600">食物查询</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-plus text-green-600"></i>
                    </div>
                    <p class="text-xs text-gray-600">记录饮食</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-pills text-purple-600"></i>
                    </div>
                    <p class="text-xs text-gray-600">用药提醒</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-chart-line text-red-600"></i>
                    </div>
                    <p class="text-xs text-gray-600">健康报告</p>
                </div>
            </div>
        </div>

        <!-- 健康趋势 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-800">健康趋势</h2>
                <span class="text-sm text-purple-600">查看更多</span>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600">血糖</span>
                        <i class="fas fa-arrow-up text-red-500 text-sm"></i>
                    </div>
                    <p class="text-xl font-bold text-gray-800">6.8</p>
                    <p class="text-xs text-gray-500">mmol/L</p>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600">体重</span>
                        <i class="fas fa-arrow-down text-green-500 text-sm"></i>
                    </div>
                    <p class="text-xl font-bold text-gray-800">68.2</p>
                    <p class="text-xs text-gray-500">kg</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar fixed bottom-0 left-0 right-0 px-4 py-2">
        <div class="flex items-center justify-around">
            <div class="text-center">
                <i class="fas fa-home text-purple-600 text-xl mb-1"></i>
                <p class="text-xs text-purple-600 font-medium">首页</p>
            </div>
            <div class="text-center">
                <i class="fas fa-utensils text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">饮食</p>
            </div>
            <div class="text-center">
                <i class="fas fa-search text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">查询</p>
            </div>
            <div class="text-center">
                <i class="fas fa-chart-bar text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">健康</p>
            </div>
            <div class="text-center">
                <i class="fas fa-user text-gray-400 text-xl mb-1"></i>
                <p class="text-xs text-gray-400">我的</p>
            </div>
        </div>
    </div>
</body>
</html>
